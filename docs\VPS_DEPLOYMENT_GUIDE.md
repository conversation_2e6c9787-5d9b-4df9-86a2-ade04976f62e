# 🚀 ToolRapter VPS Deployment Guide

## 📋 Overview

This guide provides step-by-step instructions for deploying the ToolRapter Next.js 14 application to a Hostinger VPS with enterprise-grade security and performance standards.

**Target Environment:**
- **Domain:** toolrapter.com
- **VPS:** Hostinger Ubuntu 22.04 LTS
- **IP:** ************
- **Repository:** https://github.com/MuhammadShahbaz195/ToolCrush.git

## 🎯 Prerequisites

### 1. VPS Requirements
- Ubuntu 22.04 LTS or newer
- Minimum 2GB RAM, 2 CPU cores
- 20GB+ storage space
- Root access via SSH

### 2. Domain Configuration
- Domain pointing to VPS IP (************)
- DNS A records configured:
  - `toolrapter.com` → `************`
  - `www.toolrapter.com` → `************`

### 3. GitHub Repository Access
- SSH key configured for repository access
- GitHub Actions secrets configured (see below)

## 🔐 GitHub Secrets Configuration

Configure the following secrets in your GitHub repository:

```bash
# VPS Access
VPS_SSH_KEY=your-ed25519-private-key-content

# Database
MONGODB_URI=mongodb+srv://username:<EMAIL>/toolrapter

# Authentication
NEXTAUTH_SECRET=your-cryptographically-secure-random-string-32-chars-min

# Redis (Rate Limiting)
UPSTASH_REDIS_REST_URL=https://your-redis-endpoint.upstash.io
UPSTASH_REDIS_REST_TOKEN=your-redis-token

# Email (Optional)
EMAIL_SERVER_PASSWORD=your-email-app-password

# Monitoring (Optional)
MONITORING_API_KEY=your-monitoring-api-key
```

## 🛠️ VPS Setup

### 1. Initial Server Setup

```bash
# Connect to your VPS
ssh root@************

# Clone the repository
git clone https://github.com/MuhammadShahbaz195/ToolCrush.git /tmp/toolrapter-setup
cd /tmp/toolrapter-setup

# Run the automated setup script
chmod +x scripts/setup-vps.sh
./scripts/setup-vps.sh
```

### 2. Manual Setup (Alternative)

If you prefer manual setup:

```bash
# Update system
apt update && apt upgrade -y

# Install Node.js 20
curl -fsSL https://deb.nodesource.com/setup_20.x | bash -
apt install -y nodejs

# Install PM2 and pnpm
npm install -g pm2 pnpm

# Install Nginx
apt install -y nginx

# Install Certbot for SSL
apt install -y certbot python3-certbot-nginx

# Configure firewall
ufw allow 'Nginx Full'
ufw allow OpenSSH
ufw --force enable
```

### 3. Application Directory Setup

```bash
# Create application directories
mkdir -p /var/www/toolrapter
mkdir -p /var/log/pm2
mkdir -p /var/backups/toolrapter

# Set permissions
chown -R www-data:www-data /var/www/toolrapter
chown -R www-data:www-data /var/log/pm2
chmod -R 755 /var/www/toolrapter
```

### 4. Nginx Configuration

```bash
# Copy nginx configuration
cp nginx.conf /etc/nginx/sites-available/toolrapter.com

# Enable site
ln -s /etc/nginx/sites-available/toolrapter.com /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# Test and reload
nginx -t
systemctl reload nginx
```

### 5. SSL Certificate Setup

```bash
# Obtain SSL certificate
certbot --nginx -d toolrapter.com -d www.toolrapter.com

# Setup auto-renewal
(crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet") | crontab -
```

## 🚀 Deployment Process

### 1. Automated Deployment (Recommended)

The application uses GitHub Actions for automated deployment:

```bash
# Trigger deployment by pushing to main branch
git push origin main

# Or manually trigger via GitHub Actions UI
# Go to: https://github.com/MuhammadShahbaz195/ToolCrush/actions
# Select "Enterprise VPS Deployment" workflow
# Click "Run workflow"
```

### 2. Manual Deployment

For manual deployment:

```bash
# On your local machine
npm run deploy:vps

# Or use the deployment script directly
bash scripts/deploy-vps.sh production
```

### 3. PM2 Process Management

```bash
# Start application
pm2 start ecosystem.config.js --env production

# Monitor application
pm2 monit

# View logs
pm2 logs toolrapter

# Restart application
pm2 restart toolrapter

# Stop application
pm2 stop toolrapter

# Save PM2 configuration
pm2 save

# Setup PM2 startup script
pm2 startup
```

## 📊 Monitoring and Maintenance

### 1. Health Checks

```bash
# Check application health
curl https://toolrapter.com/api/health

# Detailed health check (requires monitoring API key)
curl -H "Authorization: Bearer YOUR_MONITORING_KEY" \
     -X POST https://toolrapter.com/api/health
```

### 2. Performance Monitoring

```bash
# Run performance analysis
npm run performance:prod

# Check bundle size
npm run analyze

# Monitor system resources
htop
df -h
free -h
```

### 3. Log Management

```bash
# View application logs
pm2 logs toolrapter

# View Nginx logs
tail -f /var/log/nginx/toolrapter_access.log
tail -f /var/log/nginx/toolrapter_error.log

# View system logs
journalctl -u nginx -f
journalctl -u pm2-root -f
```

### 4. Backup Management

```bash
# View available backups
ls -la /var/backups/toolrapter/

# Manual backup
tar -czf /var/backups/toolrapter/manual-backup-$(date +%Y%m%d-%H%M%S).tar.gz \
    -C /var/www/toolrapter .

# Restore from backup
cd /var/www/toolrapter
tar -xzf /var/backups/toolrapter/backup-YYYYMMDD-HHMMSS.tar.gz
pm2 restart toolrapter
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Application Won't Start
```bash
# Check PM2 status
pm2 status

# Check application logs
pm2 logs toolrapter

# Check if port is in use
netstat -tulpn | grep :3000

# Restart application
pm2 restart toolrapter
```

#### 2. SSL Certificate Issues
```bash
# Check certificate status
certbot certificates

# Renew certificate manually
certbot renew --dry-run
certbot renew

# Test SSL configuration
nginx -t
systemctl reload nginx
```

#### 3. High Memory Usage
```bash
# Check memory usage
free -h
pm2 monit

# Restart application to clear memory
pm2 restart toolrapter

# Check for memory leaks
npm run performance
```

#### 4. Database Connection Issues
```bash
# Test MongoDB connection
mongosh "YOUR_MONGODB_URI"

# Check environment variables
pm2 env 0

# Restart with fresh environment
pm2 restart toolrapter --update-env
```

### Performance Issues

#### 1. Slow Response Times
```bash
# Check server resources
htop
iotop

# Analyze bundle size
npm run analyze

# Check Nginx configuration
nginx -t

# Monitor network
netstat -i
```

#### 2. High CPU Usage
```bash
# Check PM2 cluster status
pm2 status

# Scale application
pm2 scale toolrapter 4

# Check for infinite loops
pm2 logs toolrapter | grep -i error
```

## 🔄 Rollback Procedures

### 1. Automatic Rollback
The GitHub Actions workflow automatically rolls back on deployment failure.

### 2. Manual Rollback
```bash
# List available backups
ls -la /var/backups/toolrapter/

# Rollback to specific backup
cd /var/www/toolrapter
pm2 stop toolrapter
tar -xzf /var/backups/toolrapter/backup-YYYYMMDD-HHMMSS.tar.gz
pm2 start toolrapter
```

### 3. Git-based Rollback
```bash
# Revert to previous commit
git revert HEAD
git push origin main

# Or reset to specific commit
git reset --hard COMMIT_HASH
git push --force origin main
```

## 📞 Support

For deployment issues:
1. Check the troubleshooting section above
2. Review application logs: `pm2 logs toolrapter`
3. Check system logs: `journalctl -u nginx -f`
4. Monitor system resources: `htop`, `free -h`, `df -h`

## 🔗 Useful Commands

```bash
# Quick status check
pm2 status && systemctl status nginx && df -h

# Full system restart
pm2 restart all && systemctl restart nginx

# Update application
cd /var/www/toolrapter && git pull && npm ci --production && pm2 restart toolrapter

# Security scan
fail2ban-client status
ufw status

# Performance check
npm run performance:prod
```
