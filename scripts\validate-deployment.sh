#!/bin/bash

# =============================================================================
# TOOLRAPTER - DEPLOYMENT VALIDATION SCRIPT
# =============================================================================
# Comprehensive validation for VPS deployment pipeline
# Tests: Build process, security, performance, health checks
# Usage: ./scripts/validate-deployment.sh [local|production]

set -e  # Exit on any error

# =============================================================================
# CONFIGURATION
# =============================================================================
ENVIRONMENT=${1:-local}
PRODUCTION_URL="https://toolrapter.com"
LOCAL_URL="http://localhost:3000"
TEST_URL=$([[ "$ENVIRONMENT" == "production" ]] && echo "$PRODUCTION_URL" || echo "$LOCAL_URL")

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Test results
TESTS_PASSED=0
TESTS_FAILED=0
TESTS_WARNINGS=0

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
    ((TESTS_PASSED++))
}

log_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
    ((TESTS_WARNINGS++))
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $1"
    ((TESTS_FAILED++))
}

log_header() {
    echo -e "\n${CYAN}=== $1 ===${NC}"
}

# =============================================================================
# BUILD VALIDATION
# =============================================================================
validate_build() {
    log_header "Build Validation"
    
    # Check if build directory exists
    if [ -d ".next" ]; then
        log_success "Build directory exists"
    else
        log_error "Build directory not found - run 'npm run build' first"
        return 1
    fi
    
    # Check essential build files
    local essential_files=(
        ".next/package.json"
        ".next/BUILD_ID"
        ".next/static"
    )
    
    for file in "${essential_files[@]}"; do
        if [ -e "$file" ]; then
            log_success "Essential file exists: $file"
        else
            log_error "Missing essential file: $file"
        fi
    done
    
    # Check bundle size
    local bundle_size=$(du -sh .next 2>/dev/null | cut -f1 || echo "unknown")
    log_info "Bundle size: $bundle_size"
    
    # Validate package.json scripts
    local required_scripts=("build" "start" "performance" "deploy:vps")
    for script in "${required_scripts[@]}"; do
        if npm run | grep -q "$script"; then
            log_success "Required script exists: $script"
        else
            log_error "Missing required script: $script"
        fi
    done
}

# =============================================================================
# CONFIGURATION VALIDATION
# =============================================================================
validate_configuration() {
    log_header "Configuration Validation"
    
    # Check essential configuration files
    local config_files=(
        "next.config.js"
        "ecosystem.config.js"
        "nginx.conf"
        ".github/workflows/deploy-vps.yml"
    )
    
    for file in "${config_files[@]}"; do
        if [ -f "$file" ]; then
            log_success "Configuration file exists: $file"
        else
            log_error "Missing configuration file: $file"
        fi
    done
    
    # Validate Next.js config
    if node -e "require('./next.config.js')" 2>/dev/null; then
        log_success "next.config.js is valid"
    else
        log_error "next.config.js has syntax errors"
    fi
    
    # Validate PM2 config
    if node -e "require('./ecosystem.config.js')" 2>/dev/null; then
        log_success "ecosystem.config.js is valid"
    else
        log_error "ecosystem.config.js has syntax errors"
    fi
    
    # Check for Vercel dependencies (should be none)
    if grep -r "vercel" package.json next.config.js 2>/dev/null | grep -v "# No Vercel"; then
        log_warning "Found potential Vercel references"
    else
        log_success "No Vercel dependencies found"
    fi
}

# =============================================================================
# SECURITY VALIDATION
# =============================================================================
validate_security() {
    log_header "Security Validation"
    
    # Check security middleware
    if [ -f "src/middleware.ts" ]; then
        log_success "Security middleware exists"
        
        # Check for security features
        local security_features=("rateLimit" "CSRF" "securityHeaders")
        for feature in "${security_features[@]}"; do
            if grep -q "$feature" src/middleware.ts; then
                log_success "Security feature implemented: $feature"
            else
                log_warning "Security feature not found: $feature"
            fi
        done
    else
        log_error "Security middleware not found"
    fi
    
    # Check environment template
    if [ -f ".env.example" ]; then
        log_success "Environment template exists"
        
        # Check for sensitive data in example
        if grep -E "(password|secret|key).*=" .env.example | grep -v "your-" | grep -v "example"; then
            log_error "Potential sensitive data in .env.example"
        else
            log_success "No sensitive data in .env.example"
        fi
    else
        log_error "Environment template not found"
    fi
    
    # Check .gitignore
    if grep -q ".env" .gitignore && grep -q "node_modules" .gitignore; then
        log_success "Essential files in .gitignore"
    else
        log_error "Missing essential entries in .gitignore"
    fi
}

# =============================================================================
# HEALTH CHECK VALIDATION
# =============================================================================
validate_health_check() {
    log_header "Health Check Validation"
    
    # Check if health endpoint exists
    if [ -f "src/app/api/health/route.ts" ]; then
        log_success "Health check endpoint exists"
    else
        log_error "Health check endpoint not found"
        return 1
    fi
    
    # Test health endpoint if server is running
    if curl -f -s "$TEST_URL/api/health" > /dev/null 2>&1; then
        log_success "Health endpoint is accessible"
        
        # Check response format
        local health_response=$(curl -s "$TEST_URL/api/health")
        if echo "$health_response" | jq -e '.status' > /dev/null 2>&1; then
            log_success "Health endpoint returns valid JSON"
            
            local status=$(echo "$health_response" | jq -r '.status')
            if [ "$status" = "healthy" ]; then
                log_success "Application reports healthy status"
            else
                log_warning "Application reports status: $status"
            fi
        else
            log_error "Health endpoint returns invalid JSON"
        fi
    else
        if [ "$ENVIRONMENT" = "local" ]; then
            log_warning "Health endpoint not accessible (server may not be running)"
        else
            log_error "Health endpoint not accessible in production"
        fi
    fi
}

# =============================================================================
# PERFORMANCE VALIDATION
# =============================================================================
validate_performance() {
    log_header "Performance Validation"
    
    # Check if performance script exists
    if [ -f "scripts/performance-monitor.js" ]; then
        log_success "Performance monitoring script exists"
        
        # Run performance check if possible
        if command -v node > /dev/null && [ -d ".next" ]; then
            log_info "Running performance analysis..."
            if node scripts/performance-monitor.js > /dev/null 2>&1; then
                log_success "Performance analysis completed"
            else
                log_warning "Performance analysis failed or found issues"
            fi
        else
            log_warning "Cannot run performance analysis (missing node or build)"
        fi
    else
        log_error "Performance monitoring script not found"
    fi
    
    # Check bundle optimization
    if grep -q "optimizePackageImports" next.config.js; then
        log_success "Package import optimization enabled"
    else
        log_warning "Package import optimization not configured"
    fi
    
    # Check compression settings
    if grep -q "compress.*true" next.config.js; then
        log_success "Compression enabled"
    else
        log_warning "Compression not enabled"
    fi
}

# =============================================================================
# DEPLOYMENT PIPELINE VALIDATION
# =============================================================================
validate_deployment_pipeline() {
    log_header "Deployment Pipeline Validation"
    
    # Check GitHub Actions workflow
    local workflow_file=".github/workflows/deploy-vps.yml"
    if [ -f "$workflow_file" ]; then
        log_success "GitHub Actions workflow exists"
        
        # Check workflow structure
        local required_jobs=("security-scan" "build-and-test" "deploy" "verify")
        for job in "${required_jobs[@]}"; do
            if grep -q "$job:" "$workflow_file"; then
                log_success "Workflow job exists: $job"
            else
                log_error "Missing workflow job: $job"
            fi
        done
        
        # Check for required secrets
        local required_secrets=("VPS_SSH_KEY" "MONGODB_URI" "NEXTAUTH_SECRET")
        for secret in "${required_secrets[@]}"; do
            if grep -q "$secret" "$workflow_file"; then
                log_success "Workflow references secret: $secret"
            else
                log_error "Missing secret reference: $secret"
            fi
        done
    else
        log_error "GitHub Actions workflow not found"
    fi
    
    # Check deployment scripts
    local deploy_scripts=("scripts/deploy-vps.sh" "scripts/setup-vps.sh")
    for script in "${deploy_scripts[@]}"; do
        if [ -f "$script" ] && [ -x "$script" ]; then
            log_success "Deployment script exists and is executable: $script"
        elif [ -f "$script" ]; then
            log_warning "Deployment script exists but not executable: $script"
        else
            log_error "Missing deployment script: $script"
        fi
    done
}

# =============================================================================
# MAIN EXECUTION
# =============================================================================
main() {
    echo -e "${CYAN}🔍 ToolRapter Deployment Validation${NC}"
    echo -e "${CYAN}Environment: $ENVIRONMENT${NC}"
    echo -e "${CYAN}Test URL: $TEST_URL${NC}"
    echo -e "${CYAN}=====================================\n${NC}"
    
    # Run all validations
    validate_build
    validate_configuration
    validate_security
    validate_health_check
    validate_performance
    validate_deployment_pipeline
    
    # Summary
    log_header "Validation Summary"
    echo -e "${GREEN}✅ Tests Passed: $TESTS_PASSED${NC}"
    
    if [ $TESTS_WARNINGS -gt 0 ]; then
        echo -e "${YELLOW}⚠️  Warnings: $TESTS_WARNINGS${NC}"
    fi
    
    if [ $TESTS_FAILED -gt 0 ]; then
        echo -e "${RED}❌ Tests Failed: $TESTS_FAILED${NC}"
    fi
    
    # Overall result
    if [ $TESTS_FAILED -eq 0 ]; then
        if [ $TESTS_WARNINGS -eq 0 ]; then
            echo -e "\n${GREEN}🎉 All validations passed! Deployment ready.${NC}"
            exit 0
        else
            echo -e "\n${YELLOW}⚠️  Validations passed with warnings. Review before deployment.${NC}"
            exit 0
        fi
    else
        echo -e "\n${RED}❌ Validation failed. Fix issues before deployment.${NC}"
        exit 1
    fi
}

# Run if called directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
