'use client';

import React, { forwardRef } from 'react';
import { motion, HTMLMotionProps } from 'framer-motion';
import { useTouch } from '@/hooks/useTouch';
import { cn } from '@/lib/utils';

interface TouchableButtonProps extends Omit<HTMLMotionProps<'button'>, 'onTap'> {
  children: React.ReactNode;
  onTap?: () => void;
  onLongPress?: () => void;
  enableHapticFeedback?: boolean;
  longPressDelay?: number;
  className?: string;
  disabled?: boolean;
  variant?: 'default' | 'primary' | 'secondary' | 'outline' | 'ghost' | 'calculator';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  touchFeedback?: 'scale' | 'opacity' | 'both' | 'none';
  isPressed?: boolean;
}

const TouchableButton = forwardRef<HTMLButtonElement, TouchableButtonProps>(
  (
    {
      children,
      onTap,
      onLongPress,
      enableHapticFeedback = true,
      longPressDelay = 750,
      className,
      disabled = false,
      variant = 'default',
      size = 'md',
      touchFeedback = 'scale',
      isPressed = false,
      ...motionProps
    },
    ref
  ) => {
    const { touchState, touchHandlers, isTouchDevice } = useTouch(
      {
        onTap: disabled ? undefined : onTap,
        onLongPress: disabled ? undefined : onLongPress,
      },
      {
        longPressDelay,
        enableHapticFeedback,
      }
    );

    // Variant styles
    const variantStyles = {
      default: 'bg-background border border-border text-foreground hover:bg-accent hover:text-accent-foreground',
      primary: 'bg-primary text-primary-foreground hover:bg-primary/90',
      secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
      outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
      ghost: 'hover:bg-accent hover:text-accent-foreground',
      calculator: 'calculator-button bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 text-gray-900 dark:text-gray-100 border border-gray-300 dark:border-gray-600',
    };

    // Size styles
    const sizeStyles = {
      sm: 'h-9 px-3 text-sm',
      md: 'h-10 px-4 py-2',
      lg: 'h-11 px-8',
      xl: 'h-14 px-6 text-lg',
    };

    // Calculator specific sizing
    if (variant === 'calculator') {
      sizeStyles.sm = 'h-12 w-12 text-sm';
      sizeStyles.md = 'h-14 w-14 text-base';
      sizeStyles.lg = 'h-16 w-16 text-lg';
      sizeStyles.xl = 'h-20 w-20 text-xl';
    }

    // Touch feedback animations
    const getTouchFeedbackProps = () => {
      if (touchFeedback === 'none' || disabled) return {};

      const baseProps: any = {
        whileTap: {},
        animate: {},
      };

      if (touchFeedback === 'scale' || touchFeedback === 'both') {
        baseProps.whileTap.scale = variant === 'calculator' ? 0.95 : 0.98;
        if (touchState.isLongPressed) {
          baseProps.animate.scale = 1.02;
        }
      }

      if (touchFeedback === 'opacity' || touchFeedback === 'both') {
        baseProps.whileTap.opacity = 0.8;
      }

      return baseProps;
    };

    const { ref: touchRef, ...touchHandlersWithoutRef } = touchHandlers as any;
    const { ref: motionRef, ...motionPropsWithoutRef } = motionProps as any;

    return (
      <motion.button
        ref={ref}
        type="button"
        className={cn(
          'touch-button inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
          'user-select-none relative overflow-hidden hover-effect',
          variantStyles[variant],
          sizeStyles[size],
          {
            'touch-active': (touchState.isPressed || isPressed) && !disabled,
            'touch-long-press': touchState.isLongPressed && !disabled,
          },
          className
        )}
        disabled={disabled}
        {...touchHandlersWithoutRef}
        {...getTouchFeedbackProps()}
        {...motionPropsWithoutRef}
        style={{
          WebkitTouchCallout: 'none',
          WebkitUserSelect: 'none',
          WebkitTapHighlightColor: 'transparent',
          ...(motionProps.style || {}),
        }}
      >
        {children}

        {/* Long press indicator */}
        {touchState.isLongPressed && onLongPress && (
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="absolute top-1 right-1 w-3 h-3 bg-purple-500 rounded-full"
          />
        )}

        {/* Touch ripple effect for touch devices */}
        {isTouchDevice && (touchState.isPressed || isPressed) && !disabled && (
          <motion.div
            initial={{ scale: 0, opacity: 0.5 }}
            animate={{ scale: 2, opacity: 0 }}
            transition={{ duration: 0.6 }}
            className="absolute inset-0 bg-white/20 rounded-inherit pointer-events-none"
          />
        )}
      </motion.button>
    );
  }
);

TouchableButton.displayName = 'TouchableButton';

export { TouchableButton };
export type { TouchableButtonProps };
