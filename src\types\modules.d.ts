// Module declarations for external packages used in ToolCrush

declare module 'framer-motion' {
  import { ComponentType, ReactNode, CSSProperties } from 'react';

  export interface MotionProps {
    initial?: any;
    animate?: any;
    exit?: any;
    whileHover?: any;
    whileTap?: any;
    whileInView?: any;
    viewport?: any;
    transition?: any;
    className?: string;
    style?: CSSProperties;
    children?: ReactNode;
    onClick?: () => void;
    onHoverStart?: () => void;
    onHoverEnd?: () => void;
    [key: string]: any;
  }

  export const motion: {
    div: ComponentType<MotionProps>;
    section: ComponentType<MotionProps>;
    article: ComponentType<MotionProps>;
    header: ComponentType<MotionProps>;
    main: ComponentType<MotionProps>;
    footer: ComponentType<MotionProps>;
    nav: ComponentType<MotionProps>;
    aside: ComponentType<MotionProps>;
    h1: ComponentType<MotionProps>;
    h2: ComponentType<MotionProps>;
    h3: ComponentType<MotionProps>;
    h4: ComponentType<MotionProps>;
    h5: ComponentType<MotionProps>;
    h6: ComponentType<MotionProps>;
    p: ComponentType<MotionProps>;
    span: ComponentType<MotionProps>;
    a: ComponentType<MotionProps>;
    button: ComponentType<MotionProps>;
    img: ComponentType<MotionProps>;
    ul: ComponentType<MotionProps>;
    li: ComponentType<MotionProps>;
    form: ComponentType<MotionProps>;
    input: ComponentType<MotionProps>;
    textarea: ComponentType<MotionProps>;
    [key: string]: ComponentType<MotionProps>;
  };

  export interface AnimatePresenceProps {
    children?: ReactNode;
    initial?: boolean;
    exitBeforeEnter?: boolean;
    mode?: 'wait' | 'sync' | 'popLayout';
  }

  export const AnimatePresence: ComponentType<AnimatePresenceProps>;
}

declare module 'lucide-react' {
  import { ComponentType, SVGProps } from 'react';

  export interface LucideProps extends SVGProps<SVGSVGElement> {
    size?: string | number;
    color?: string;
    strokeWidth?: string | number;
    className?: string;
  }

  export const ArrowRight: ComponentType<LucideProps>;
  export const BookOpen: ComponentType<LucideProps>;
  export const Users: ComponentType<LucideProps>;
  export const Star: ComponentType<LucideProps>;
  export const Zap: ComponentType<LucideProps>;
  export const Menu: ComponentType<LucideProps>;
  export const X: ComponentType<LucideProps>;
  export const Search: ComponentType<LucideProps>;
  export const Home: ComponentType<LucideProps>;
  export const Settings: ComponentType<LucideProps>;
  export const User: ComponentType<LucideProps>;
  export const Mail: ComponentType<LucideProps>;
  export const Phone: ComponentType<LucideProps>;
  export const MapPin: ComponentType<LucideProps>;
  export const Calendar: ComponentType<LucideProps>;
  export const Clock: ComponentType<LucideProps>;
  export const Check: ComponentType<LucideProps>;
  export const ChevronDown: ComponentType<LucideProps>;
  export const ChevronUp: ComponentType<LucideProps>;
  export const ChevronLeft: ComponentType<LucideProps>;
  export const ChevronRight: ComponentType<LucideProps>;
  export const Plus: ComponentType<LucideProps>;
  export const Minus: ComponentType<LucideProps>;
  export const Edit: ComponentType<LucideProps>;
  export const Trash: ComponentType<LucideProps>;
  export const Download: ComponentType<LucideProps>;
  export const Upload: ComponentType<LucideProps>;
  export const Share: ComponentType<LucideProps>;
  export const Copy: ComponentType<LucideProps>;
  export const ExternalLink: ComponentType<LucideProps>;
  export const Eye: ComponentType<LucideProps>;
  export const EyeOff: ComponentType<LucideProps>;
  export const Heart: ComponentType<LucideProps>;
  export const ThumbsUp: ComponentType<LucideProps>;
  export const MessageCircle: ComponentType<LucideProps>;
  export const Send: ComponentType<LucideProps>;
  export const Filter: ComponentType<LucideProps>;
  export const SortAsc: ComponentType<LucideProps>;
  export const SortDesc: ComponentType<LucideProps>;
  export const Grid: ComponentType<LucideProps>;
  export const List: ComponentType<LucideProps>;
  export const Tag: ComponentType<LucideProps>;
  export const Folder: ComponentType<LucideProps>;
  export const File: ComponentType<LucideProps>;
  export const Image: ComponentType<LucideProps>;
  export const Video: ComponentType<LucideProps>;
  export const Music: ComponentType<LucideProps>;
  export const Lock: ComponentType<LucideProps>;
  export const Unlock: ComponentType<LucideProps>;
  export const Shield: ComponentType<LucideProps>;
  export const AlertTriangle: ComponentType<LucideProps>;
  export const Info: ComponentType<LucideProps>;
  export const HelpCircle: ComponentType<LucideProps>;
  export const Loader: ComponentType<LucideProps>;
  export const RefreshCw: ComponentType<LucideProps>;
  export const MoreHorizontal: ComponentType<LucideProps>;
  export const MoreVertical: ComponentType<LucideProps>;

  // Add more icons as needed
  [key: string]: ComponentType<LucideProps>;
}

declare module 'next/link' {
  import { ComponentType, ReactNode, AnchorHTMLAttributes } from 'react';

  export interface LinkProps extends Omit<AnchorHTMLAttributes<HTMLAnchorElement>, 'href'> {
    href: string;
    as?: string;
    replace?: boolean;
    scroll?: boolean;
    shallow?: boolean;
    passHref?: boolean;
    prefetch?: boolean;
    locale?: string | false;
    children: ReactNode;
    legacyBehavior?: boolean;
  }

  const Link: ComponentType<LinkProps>;
  export default Link;
}