#!/usr/bin/env node

// =============================================================================
// TOOLRAPTER - PERFORMANCE MONITORING SCRIPT
// =============================================================================
// Enterprise-grade performance monitoring for production deployment
// Monitors: Bundle size, Core Web Vitals, Load times, Memory usage
// Usage: node scripts/performance-monitor.js [url]

const https = require('https');
const fs = require('fs');
const path = require('path');

// Configuration
const DEFAULT_URL = 'https://toolrapter.com';
const PERFORMANCE_THRESHOLDS = {
  // Core Web Vitals thresholds (Google standards)
  LCP: 2500,      // Largest Contentful Paint (ms)
  FID: 100,       // First Input Delay (ms)
  CLS: 0.1,       // Cumulative Layout Shift
  FCP: 1800,      // First Contentful Paint (ms)
  TTI: 3800,      // Time to Interactive (ms)
  TBT: 200,       // Total Blocking Time (ms)
  
  // Custom thresholds
  LOAD_TIME: 5000,    // Total page load time (ms)
  BUNDLE_SIZE: 500,   // Bundle size (KB)
  MEMORY_USAGE: 50,   // Memory usage (MB)
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// =============================================================================
// BUNDLE SIZE ANALYSIS
// =============================================================================
function analyzeBundleSize() {
  logInfo('Analyzing bundle size...');
  
  const nextDir = path.join(process.cwd(), '.next');
  const staticDir = path.join(nextDir, 'static');
  
  if (!fs.existsSync(nextDir)) {
    logError('Build directory not found. Run "npm run build" first.');
    return null;
  }
  
  let totalSize = 0;
  const bundleInfo = {
    chunks: [],
    totalSize: 0,
    gzippedSize: 0,
  };
  
  function getDirectorySize(dirPath) {
    let size = 0;
    
    if (!fs.existsSync(dirPath)) return size;
    
    const files = fs.readdirSync(dirPath);
    
    for (const file of files) {
      const filePath = path.join(dirPath, file);
      const stats = fs.statSync(filePath);
      
      if (stats.isDirectory()) {
        size += getDirectorySize(filePath);
      } else {
        size += stats.size;
        
        if (file.endsWith('.js')) {
          bundleInfo.chunks.push({
            name: file,
            size: Math.round(stats.size / 1024), // KB
          });
        }
      }
    }
    
    return size;
  }
  
  totalSize = getDirectorySize(staticDir);
  bundleInfo.totalSize = Math.round(totalSize / 1024); // KB
  
  // Sort chunks by size
  bundleInfo.chunks.sort((a, b) => b.size - a.size);
  
  // Check against threshold
  if (bundleInfo.totalSize > PERFORMANCE_THRESHOLDS.BUNDLE_SIZE) {
    logWarning(`Bundle size (${bundleInfo.totalSize}KB) exceeds threshold (${PERFORMANCE_THRESHOLDS.BUNDLE_SIZE}KB)`);
  } else {
    logSuccess(`Bundle size: ${bundleInfo.totalSize}KB (within threshold)`);
  }
  
  // Show largest chunks
  logInfo('Largest chunks:');
  bundleInfo.chunks.slice(0, 5).forEach(chunk => {
    console.log(`  ${chunk.name}: ${chunk.size}KB`);
  });
  
  return bundleInfo;
}

// =============================================================================
// PERFORMANCE TESTING
// =============================================================================
async function testPagePerformance(url) {
  logInfo(`Testing performance for: ${url}`);
  
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    
    const request = https.get(url, (response) => {
      let data = '';
      
      response.on('data', (chunk) => {
        data += chunk;
      });
      
      response.on('end', () => {
        const endTime = Date.now();
        const loadTime = endTime - startTime;
        
        const performance = {
          url,
          statusCode: response.statusCode,
          loadTime,
          contentLength: data.length,
          headers: response.headers,
        };
        
        // Check load time threshold
        if (loadTime > PERFORMANCE_THRESHOLDS.LOAD_TIME) {
          logWarning(`Load time (${loadTime}ms) exceeds threshold (${PERFORMANCE_THRESHOLDS.LOAD_TIME}ms)`);
        } else {
          logSuccess(`Load time: ${loadTime}ms (within threshold)`);
        }
        
        resolve(performance);
      });
    });
    
    request.on('error', (error) => {
      logError(`Request failed: ${error.message}`);
      reject(error);
    });
    
    request.setTimeout(10000, () => {
      logError('Request timeout');
      request.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

// =============================================================================
// MEMORY USAGE ANALYSIS
// =============================================================================
function analyzeMemoryUsage() {
  logInfo('Analyzing memory usage...');
  
  const memUsage = process.memoryUsage();
  const memoryInfo = {
    heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
    heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
    external: Math.round(memUsage.external / 1024 / 1024), // MB
    rss: Math.round(memUsage.rss / 1024 / 1024), // MB
  };
  
  // Check memory usage threshold
  if (memoryInfo.heapUsed > PERFORMANCE_THRESHOLDS.MEMORY_USAGE) {
    logWarning(`Memory usage (${memoryInfo.heapUsed}MB) exceeds threshold (${PERFORMANCE_THRESHOLDS.MEMORY_USAGE}MB)`);
  } else {
    logSuccess(`Memory usage: ${memoryInfo.heapUsed}MB (within threshold)`);
  }
  
  logInfo(`Memory details:`);
  console.log(`  Heap Used: ${memoryInfo.heapUsed}MB`);
  console.log(`  Heap Total: ${memoryInfo.heapTotal}MB`);
  console.log(`  External: ${memoryInfo.external}MB`);
  console.log(`  RSS: ${memoryInfo.rss}MB`);
  
  return memoryInfo;
}

// =============================================================================
// REPORT GENERATION
// =============================================================================
function generateReport(bundleInfo, performanceInfo, memoryInfo) {
  const report = {
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    thresholds: PERFORMANCE_THRESHOLDS,
    results: {
      bundle: bundleInfo,
      performance: performanceInfo,
      memory: memoryInfo,
    },
    summary: {
      passed: 0,
      warnings: 0,
      errors: 0,
    },
  };
  
  // Calculate summary
  if (bundleInfo && bundleInfo.totalSize <= PERFORMANCE_THRESHOLDS.BUNDLE_SIZE) {
    report.summary.passed++;
  } else {
    report.summary.warnings++;
  }
  
  if (performanceInfo && performanceInfo.loadTime <= PERFORMANCE_THRESHOLDS.LOAD_TIME) {
    report.summary.passed++;
  } else {
    report.summary.warnings++;
  }
  
  if (memoryInfo && memoryInfo.heapUsed <= PERFORMANCE_THRESHOLDS.MEMORY_USAGE) {
    report.summary.passed++;
  } else {
    report.summary.warnings++;
  }
  
  // Save report
  const reportPath = path.join(process.cwd(), 'performance-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  logInfo(`Performance report saved to: ${reportPath}`);
  
  // Display summary
  log('\n📊 Performance Summary:', 'cyan');
  logSuccess(`Passed: ${report.summary.passed}`);
  if (report.summary.warnings > 0) {
    logWarning(`Warnings: ${report.summary.warnings}`);
  }
  if (report.summary.errors > 0) {
    logError(`Errors: ${report.summary.errors}`);
  }
  
  return report;
}

// =============================================================================
// MAIN EXECUTION
// =============================================================================
async function main() {
  const url = process.argv[2] || DEFAULT_URL;
  
  log('🚀 ToolRapter Performance Monitor', 'cyan');
  log('=====================================', 'cyan');
  
  try {
    // Analyze bundle size
    const bundleInfo = analyzeBundleSize();
    
    // Test page performance
    let performanceInfo = null;
    try {
      performanceInfo = await testPagePerformance(url);
    } catch (error) {
      logWarning('Performance test skipped (network error)');
    }
    
    // Analyze memory usage
    const memoryInfo = analyzeMemoryUsage();
    
    // Generate report
    const report = generateReport(bundleInfo, performanceInfo, memoryInfo);
    
    // Exit with appropriate code
    const hasWarningsOrErrors = report.summary.warnings > 0 || report.summary.errors > 0;
    process.exit(hasWarningsOrErrors ? 1 : 0);
    
  } catch (error) {
    logError(`Performance monitoring failed: ${error.message}`);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  analyzeBundleSize,
  testPagePerformance,
  analyzeMemoryUsage,
  generateReport,
};
