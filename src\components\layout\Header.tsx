"use client"; // Indicate that this is a client-side component

import Link from "next/link";
import { useRouter, usePathname } from "next/navigation";
import { useLoading } from "@/contexts/LoadingContext";
import { Progress } from "@/components/ui/nprogress";
import { motion, HTMLMotionProps } from "framer-motion";
import {
  FiHome,
  FiTool,
  FiLogIn,
  FiUserPlus,

  FiChevronDown,
  FiFileText,
  FiFile,
  FiImage,
  FiArchive,
  FiEdit,
  FiSliders,
  FiPieChart,
  FiCamera,
  FiGitMerge,
  FiGrid,
  FiBook,
  FiCode,
  FiUser,

  FiLogOut,
  FiShield,
  FiLayout,

  FiRotateCw,
  FiGlobe,
} from "react-icons/fi";
import {
  Sun,
  Moon,
  Calculator,
  Menu,
  X,
} from "lucide-react";
import * as LucideIcons from "lucide-react";
import { useState, useEffect, useCallback } from "react";

import { Button } from "@/components/ui/button";
import { useTheme } from "@/hooks/useTheme";
import { useAuth } from "@/hooks/useAuth";
import { ALL_CALCULATORS, categoryLabels } from "@/data/calculators";
import { ALL_TOOLS } from "@/data/tools";
import SmartSearch from "./SmartSearch";
import { getToolRoute, getCalculatorRoute } from "@/lib/routing";
import { useSwipeable } from 'react-swipeable';
import { hapticFeedback } from '@/hooks/useTouch';

// Type definitions
interface NavItem {
  name: string;
  path: string;
  icon: React.ReactNode;
  className?: string;
}

interface ToolCategory {
  name: string;
  tools: NavItem[];
}

interface CalculatorCategory {
  name: string;
  calculators: NavItem[];
}

const Header = () => {
  // Access user and isAdmin from Redux (removed isEditor as per new instructions)
  const { user, isAdmin, logout } = useAuth();
  const { theme, toggleTheme } = useTheme();
  const router = useRouter();
  const pathname = usePathname();
  const { startLoading, stopLoading } = useLoading();

  // Mobile menu state
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Swipe handlers for edge navigation
  const swipeHandlers = useSwipeable({
    onSwipedRight: (eventData) => {
      // Swipe from left edge to open mobile menu
      if (eventData.initial[0] < 50 && !mobileMenuOpen) {
        setMobileMenuOpen(true);
        hapticFeedback.medium();
      }
    },
    onSwipedLeft: () => {
      // Swipe left to close mobile menu
      if (mobileMenuOpen) {
        setMobileMenuOpen(false);
        hapticFeedback.light();
      }
    },
    trackMouse: false, // Only track touch for mobile
    delta: 50,
  });

  // Close mobile menu when window is resized to desktop size
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 768 && mobileMenuOpen) {
        setMobileMenuOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [mobileMenuOpen]);

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (mobileMenuOpen && !target.closest('.mobile-menu') && !target.closest('.mobile-menu-button')) {
        setMobileMenuOpen(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, [mobileMenuOpen]);

  // Prevent scrolling when mobile menu is open
  useEffect(() => {
    if (mobileMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [mobileMenuOpen]);

  // Function to render the appropriate icon based on the icon name
  const renderIcon = (iconName: string, className: string = "mr-2") => {
    // Convert kebab-case to PascalCase for Lucide icons
    const pascalCaseName = iconName
      .split("-")
      .map(part => part.charAt(0).toUpperCase() + part.slice(1))
      .join("");

    // Get the icon component from Lucide
    const IconComponent = (LucideIcons as any)[pascalCaseName];

    if (IconComponent) {
      return <IconComponent className={className} />;
    }

    // Fallback to a default icon if the specified icon doesn't exist
    return <LucideIcons.Calculator className={className} />;
  };

  // Helper function to get appropriate icon for tool category
  const getToolIcon = (category: string, toolId: string) => {
    // Special cases for specific tools
    const specialIcons: Record<string, JSX.Element> = {
      "pdf-to-word": <FiFileText className="mr-2" />,
      "pdf-to-powerpoint": <FiSliders className="mr-2" />,
      "pdf-to-excel": <FiPieChart className="mr-2" />,
      "pdf-to-jpg": <FiImage className="mr-2" />,
      "pdf-to-pdfa": <FiArchive className="mr-2" />,
      "word-to-pdf": <FiEdit className="mr-2" />,
      "powerpoint-to-pdf": <FiSliders className="mr-2" />,
      "excel-to-pdf": <FiPieChart className="mr-2" />,
      "jpg-to-pdf": <FiCamera className="mr-2" />,
      "html-to-pdf": <FiCode className="mr-2" />,
      "merge-pdf": <FiGitMerge className="mr-2" />,
      "split-pdf": <FiGitMerge className="mr-2 transform rotate-90" />,
      "compress-pdf": <FiFile className="mr-2" />,
      "rotate-pdf": <FiRotateCw className="mr-2" />,
    };

    if (specialIcons[toolId]) {
      return specialIcons[toolId];
    }

    // Default icons by category
    switch (category) {
      case 'pdf':
        return <FiFileText className="mr-2" />;
      case 'office':
        return <FiEdit className="mr-2" />;
      case 'image':
        return <FiImage className="mr-2" />;
      case 'web':
        return <FiGlobe className="mr-2" />;
      default:
        return <FiTool className="mr-2" />;
    }
  };

  // Generate tool items from ALL_TOOLS data using centralized routing
  const allTools: NavItem[] = ALL_TOOLS.map((tool) => ({
    name: tool.title,
    path: getToolRoute(tool.id),
    icon: getToolIcon(tool.category, tool.id),
  }));



  // Group calculators by category and limit to 3 per category for dropdown
  const calculatorCategories: CalculatorCategory[] = Object.entries(
    ALL_CALCULATORS.reduce((acc, calculator) => {
      if (!acc[calculator.category]) {
        acc[calculator.category] = [];
      }
      acc[calculator.category].push({
        name: calculator.title,
        path: getCalculatorRoute(calculator.id),
        icon: renderIcon(calculator.icon),
      });
      return acc;
    }, {} as Record<string, NavItem[]>)
  ).map(([category, calculators]) => ({
    name: categoryLabels[category] || category,
    // Limit to 3 calculators per category in dropdown
    calculators: calculators.slice(0, 3),
  }));

  const toolCategories: ToolCategory[] = [
    {
      name: "PDF Conversions",
      tools: allTools.filter((tool) => tool.path.includes("to-pdf")),
    },
    {
      name: "From PDF",
      tools: allTools.filter((tool) => tool.path.includes("pdf-to")),
    },
    {
      name: "PDF Operations",
      tools: allTools.filter(
        (tool) =>
          !tool.path.includes("to-pdf") &&
          !tool.path.includes("pdf-to") &&
          !tool.path.includes("ocr") &&
          !tool.path.includes("scan")
      ),
    },
    {
      name: "Specialized Tools",
      tools: allTools.filter(
        (tool) =>
          tool.path.includes("ocr") ||
          tool.path.includes("scan") ||
          tool.path.includes("compare")
      ),
    },
  ];

  // Handle navigation with loading indicator using Next.js router
  const handleNavigation = useCallback(async (path: string, e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
    }

    if (pathname === path) {
      // If clicking on current path, just close mobile menu if open
      if (mobileMenuOpen) {
        setMobileMenuOpen(false);
      }
      return;
    }

    // Start loading indicator
    startLoading();
    Progress.start();

    // Close mobile menu if open
    if (mobileMenuOpen) {
      setMobileMenuOpen(false);
    }

    // Use Next.js router for SPA navigation
    try {
      // Push to the new route
      await router.push(path);

      // Set a timeout to stop loading if navigation takes too long
      const timeoutId = setTimeout(() => {
        stopLoading();
        Progress.done();
      }, 3000); // 3 second timeout

      // Clean up timeout on successful navigation
      window.addEventListener('routeChangeComplete', () => {
        clearTimeout(timeoutId);
      }, { once: true });

    } catch (error) {
      console.error('Navigation error:', error);
      // Fallback to direct navigation if router fails
      window.location.href = path;
    }
  }, [pathname, router, startLoading, stopLoading, mobileMenuOpen, setMobileMenuOpen]);

  // Handle logout with improved navigation
  const handleLogout = async (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();

    // Close mobile menu if open
    if (mobileMenuOpen) {
      setMobileMenuOpen(false);
    }

    try {
      // Start loading indicator
      startLoading();
      Progress.start();

      // Call the logout function
      await logout();

      // Use Next.js router for SPA navigation
      try {
        // Push to the login route
        await router.push('/login');

        // Set a timeout to stop loading if navigation takes too long
        const timeoutId = setTimeout(() => {
          stopLoading();
          Progress.done();
        }, 3000); // 3 second timeout

        // Clean up timeout on successful navigation
        window.addEventListener('routeChangeComplete', () => {
          clearTimeout(timeoutId);
        }, { once: true });

      } catch (error) {
        console.error('Navigation error after logout:', error);
        // Fallback to direct navigation if router fails
        window.location.href = '/login';
      }
    } catch (error) {
      console.error("Logout failed:", error);
      stopLoading();
      Progress.done();

      // Even if logout fails, try to navigate to login page
      try {
        await router.push('/login');
      } catch (navError) {
        console.error('Navigation error:', navError);
        window.location.href = '/login';
      }
    }
  };

  const navItems: NavItem[] = [
    { name: "Home", path: "/", icon: <FiHome className="mr-2" /> },
    // Blog is now a dropdown, so we remove it from regular nav items
    // { name: "Contact", path: "/contact", icon: <FiMail className="mr-2" /> },

    // Show login/signup buttons for non-authenticated users
    ...(!user
      ? [
          { name: "Login", path: "/login", icon: <FiLogIn className="mr-2" /> },
          {
            name: "Sign Up",
            path: "/register",
            icon: <FiUserPlus className="mr-2" />,
            className:
              "px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",
          },
        ]
      : []),
  ];

  const motionHeaderProps: HTMLMotionProps<"header"> = {
    initial: { y: -100 },
    animate: { y: 0 },
    transition: { type: "spring", stiffness: 300, damping: 20 },
    className: `${theme === "dark" ? "bg-gray-900 text-gray-200 border-gray-800" : "bg-white text-gray-800 border-gray-200"} border-b transition-all duration-500 ease-in-out shadow-sm sticky top-0 z-50`,
  };

  return (
    <motion.header {...motionHeaderProps} {...swipeHandlers}>
      <div className="container mx-auto px-1 gap-3 sm:px-1 md:px-2 py-1 sm:py-1.5 md:py-2 flex justify-between">
        <button onClick={() => handleNavigation('/')} className="flex items-center group">
          <motion.span
            whileHover={{ rotate: 15 }}
            className="ml-1  sm:ml-2 md:ml-2  lg:ml-3 xl:ml-5 text-lg sm:text-xl md:text-2xl font-semibold text-primary group-hover:text-primary/80 transition-all duration-500 ease-in-out"
          >
            ToolCrush
          </motion.span>
        </button>

        {/* Mobile menu button - show on medium screens and below */}
        <div className="lg:hidden">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            className="mobile-menu-button relative z-50"
            aria-label={mobileMenuOpen ? "Close menu" : "Open menu"}
          >
            {mobileMenuOpen ? (
              <X className="h-6 w-6 text-foreground" />
            ) : (
              <Menu className="h-6 w-6 text-foreground" />
            )}
          </Button>
        </div>

        {/* Desktop navigation - show on large screens and above */}
        <nav className="hidden lg:block">
          <ul className="flex gap-2 sm:gap-2 md:gap-4 lg:gap-6 items-center text-sm sm:text-base md:text-md lg:text-lg">
            {/* Tools dropdown */}
            <li className="relative group">
              <div className={`flex items-center transition-all duration-300 ease-in-out cursor-pointer ${
                theme === "dark"
                  ? "text-gray-300 hover:text-blue-400"
                  : "text-gray-700 hover:text-blue-600"
              }`}>
                <FiTool className={`mr-2 size-4 transition-transform duration-200 ease-in-out group-hover:scale-110 ${
                  theme === "dark" ? "group-hover:text-blue-400" : "group-hover:text-blue-600"
                }`} />
                Tools{" "}
                <FiChevronDown className="ml-1 transition-transform duration-200 ease-in-out group-hover:rotate-180" />
              </div>

              <motion.div
                initial={{ opacity: 0, y: 10, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.25, ease: "easeOut" }}
                className={`absolute left-0 mt-2 w-[350px] max-h-[400px] overflow-y-auto rounded-lg shadow-xl py-3 z-50 opacity-0 group-hover:opacity-100 invisible group-hover:visible transition-all duration-300 ease-in-out grid grid-cols-1 sm:grid-cols-2 gap-4 p-4 scrollbar-thin pointer-events-auto ${
                  theme === "dark"
                    ? "bg-gray-800 scrollbar-thumb-gray-600 hover:scrollbar-thumb-gray-500"
                    : "bg-white scrollbar-thumb-gray-300 hover:scrollbar-thumb-gray-400"
                }`}
              >
                {toolCategories.map((category) => (
                  <div key={category.name} className="space-y-2">
                    <h4 className={`font-semibold mb-2 px-2 duration-300 ease-in-out ${
                      theme === "dark" ? "text-gray-200" : "text-gray-900"
                    }`}>
                      {category.name}
                    </h4>
                    <div className="space-y-1">
                      {category.tools.map((tool) => (
                        <button
                          key={tool.name}
                          onClick={() => handleNavigation(tool.path)}
                          className={`flex items-center px-3 py-2 rounded transition-all duration-300 ease-in-out group ${
                            theme === "dark"
                              ? "text-gray-300 hover:bg-gray-700 hover:text-blue-400"
                              : "text-gray-700 hover:bg-blue-50 hover:text-blue-600"
                          }`}
                        >
                          <span className="transition-transform duration-300 ease-in-out group-hover:scale-110 mr-2">
                            {tool.icon}
                          </span>
                          <span>{tool.name}</span>
                        </button>
                      ))}
                    </div>
                  </div>
                ))}
                <div className={`col-span-2 pt-3 mt-2 transition-colors duration-300 ease-in-out ${
                  theme === "dark" ? "border-t border-gray-700" : "border-t border-gray-200"
                }`}>
                  <button
                    onClick={() => handleNavigation('/tools')}
                    className={`flex items-center justify-center px-4 py-2 rounded-md transition-all duration-300 ease-in-out transform hover:scale-105 ${
                      theme === "dark"
                        ? "bg-blue-600 text-white hover:bg-blue-700"
                        : "bg-blue-600 text-white hover:bg-blue-700"
                    }`}
                  >
                    <FiTool className="mr-2 transition-transform duration-300 ease-in-out group-hover:rotate-12" />
                    View All Tools
                  </button>
                </div>
              </motion.div>
            </li>

            {/* Calculators dropdown */}
            <li className="relative group">
              <div className={`flex items-center transition-all duration-300 ease-in-out cursor-pointer ${
                theme === "dark"
                  ? "text-gray-300 hover:text-blue-400"
                  : "text-gray-700 hover:text-blue-600"
              }`}>
                <Calculator className={`mr-2 size-4 transition-transform duration-200 ease-in-out group-hover:scale-110 ${
                  theme === "dark" ? "group-hover:text-blue-400" : "group-hover:text-blue-600"
                }`} />
                Calculators{" "}
                <FiChevronDown className="ml-1 transition-transform duration-200 ease-in-out group-hover:rotate-180" />
              </div>

              <motion.div
                initial={{ opacity: 0, y: 10, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.25, ease: "easeOut" }}
                className={`absolute left-0 mt-2 w-[350px] max-h-[400px] overflow-y-auto rounded-lg shadow-xl py-3 z-50 opacity-0 group-hover:opacity-100 invisible group-hover:visible transition-all duration-300 ease-in-out grid grid-cols-1 sm:grid-cols-2 gap-4 p-4 scrollbar-thin pointer-events-auto ${
                  theme === "dark"
                    ? "bg-gray-800 scrollbar-thumb-gray-600 hover:scrollbar-thumb-gray-500"
                    : "bg-white scrollbar-thumb-gray-300 hover:scrollbar-thumb-gray-400"
                }`}
              >
                {calculatorCategories.map((category) => (
                  <div key={category.name} className="space-y-2">
                    <h4 className={`font-semibold mb-2 px-2 duration-300 ease-in-out ${
                      theme === "dark" ? "text-gray-200" : "text-gray-900"
                    }`}>
                      {category.name}
                    </h4>
                    <div className="space-y-1">
                      {category.calculators.map((calculator) => (
                        <button
                          key={calculator.name}
                          onClick={() => handleNavigation(calculator.path)}
                          className={`flex items-center px-3 py-2 rounded transition-all duration-300 ease-in-out group ${
                            theme === "dark"
                              ? "text-gray-300 hover:bg-gray-700 hover:text-blue-400"
                              : "text-gray-700 hover:bg-blue-50 hover:text-blue-600"
                          }`}
                        >
                          <span className="transition-transform duration-300 ease-in-out group-hover:scale-110 mr-2">
                            {calculator.icon}
                          </span>
                          <span>{calculator.name}</span>
                        </button>
                      ))}
                      <button
                        onClick={() => handleNavigation(`/calculators#${category.name.toLowerCase().replace(/\s+/g, '-')}`)}
                        className={`flex items-center px-3 py-2 rounded transition-all duration-300 ease-in-out group ${
                          theme === "dark"
                            ? "text-blue-400 hover:bg-gray-700"
                            : "text-blue-600 hover:bg-blue-50"
                        }`}
                      >
                        <span className="text-xs">View all {category.name.toLowerCase()}</span>
                      </button>
                    </div>
                  </div>
                ))}
                <div className={`col-span-2 pt-3 mt-2 transition-colors duration-300 ease-in-out ${
                  theme === "dark" ? "border-t border-gray-700" : "border-t border-gray-200"
                }`}>
                  <div className="grid grid-cols-2 gap-2">
                    <button
                      onClick={() => handleNavigation('/calculators')}
                      className={`flex items-center  px-4 py-2 rounded-md transition-all duration-300 ease-in-out transform hover:scale-105 ${
                        theme === "dark"
                          ? "bg-blue-600 text-white hover:bg-blue-700"
                          : "bg-blue-600 text-white hover:bg-blue-700"
                      }`}
                    >
                      <Calculator className="mr-2 transition-transform duration-300 ease-in-out group-hover:rotate-12" />
                      All Calculators
                    </button>
                    {/* <button
                      onClick={() => handleNavigation('/calculators/all')}
                      className={`flex items-center justify-center px-4 py-2 rounded-md transition-all duration-300 ease-in-out transform hover:scale-105 ${
                        theme === "dark"
                          ? "bg-blue-500 text-white hover:bg-blue-600"
                          : "bg-blue-500 text-white hover:bg-blue-600"
                      }`}
                    >
                      <FiGrid className="mr-2 transition-transform duration-300 ease-in-out group-hover:rotate-12" />
                      Categories
                    </button> */}
                  </div>
                </div>
              </motion.div>
            </li>

            {/* Blog dropdown */}
            <li className="relative group">
              <div className={`flex items-center transition-all duration-300 ease-in-out cursor-pointer ${
                theme === "dark"
                  ? "text-gray-300 hover:text-purple-400"
                  : "text-gray-700 hover:text-purple-600"
              }`}>
                <FiBook className={`mr-2 size-4 transition-transform duration-200 ease-in-out group-hover:scale-110 ${
                  theme === "dark" ? "group-hover:text-purple-400" : "group-hover:text-purple-600"
                }`} />
                Blog{" "}
                <FiChevronDown className="ml-1 transition-transform duration-200 ease-in-out group-hover:rotate-180" />
              </div>

              <motion.div
                initial={{ opacity: 0, y: 10, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.25, ease: "easeOut" }}
                className={`absolute left-0 mt-2 w-[280px] rounded-lg shadow-xl py-3 z-50 opacity-0 group-hover:opacity-100 invisible group-hover:visible transition-all duration-300 ease-in-out p-4 ${
                  theme === "dark"
                    ? "bg-gray-800"
                    : "bg-white"
                }`}
              >
                <div className="space-y-2">
                  <h4 className={`font-semibold mb-3 px-2 duration-300 ease-in-out ${
                    theme === "dark" ? "text-gray-200" : "text-gray-900"
                  }`}>
                    Blog
                  </h4>
                  <div className="space-y-1">
                    <button
                      onClick={() => handleNavigation('/blog')}
                      className={`flex items-center px-3 py-2 rounded transition-all duration-300 ease-in-out group ${
                        theme === "dark"
                          ? "text-gray-300 hover:bg-gray-700 hover:text-purple-400"
                          : "text-gray-700 hover:bg-purple-50 hover:text-purple-600"
                      }`}
                    >
                      <FiHome className="mr-2 transition-transform duration-300 ease-in-out group-hover:scale-110" />
                      <span>Blog Home</span>
                    </button>
                    <button
                      onClick={() => handleNavigation('/blog/all')}
                      className={`flex items-center px-3 py-2 rounded transition-all duration-300 ease-in-out group ${
                        theme === "dark"
                          ? "text-gray-300 hover:bg-gray-700 hover:text-purple-400"
                          : "text-gray-700 hover:bg-purple-50 hover:text-purple-600"
                      }`}
                    >
                      <FiGrid className="mr-2 transition-transform duration-300 ease-in-out group-hover:scale-110" />
                      <span>View All Articles</span>
                    </button>
                  </div>
                </div>
              </motion.div>
            </li>

            <Button
              variant="ghost"
              size="icon"
              className={`transition-all duration-300 ease-in-out rounded-full ${
                theme === "dark"
                  ? "text-gray-300 hover:bg-gray-700 hover:text-white"
                  : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
              }`}
              onClick={toggleTheme}
            >
              {theme === "dark" ? (
                <Sun className="h-5 w-5 transition-transform duration-500 ease-in-out hover:rotate-90" />
              ) : (
                <Moon className="h-5 w-5 transition-transform duration-500 ease-in-out hover:rotate-12" />
              )}
              <span className="sr-only">Toggle theme</span>
            </Button>

            {/* Regular navigation items */}
            {navItems.map((item) => (
              <li key={item.name}>
                <button
                  onClick={() => handleNavigation(item.path)}
                  className={`flex items-center transition-all duration-300 ease-in-out group relative ${
                    item.className || "text-foreground hover:text-primary"
                  }`}
                >
                  <span className="transition-transform duration-300 ease-in-out group-hover:scale-110">
                    {item.icon}
                  </span>
                  <span>{item.name}</span>
                  {!item.className && (
                    <span className="absolute -bottom-1 left-0 h-0.5 w-0 bg-gradient-to-r from-primary to-primary/70 transition-all duration-300 ease-in-out group-hover:w-full"></span>
                  )}
                </button>
              </li>
            ))}

            {/* User profile dropdown for authenticated users */}
            {user && (
              <li className="relative group">
                <div className="flex items-center text-foreground hover:text-primary transition-all duration-300 ease-in-out cursor-pointer">
                  <div className="w-8 h-8 rounded-full bg-primary/20 text-primary flex items-center justify-center mr-2 transition-all duration-300 ease-in-out">
                    {user.image ? (
                      <img
                        src={user.image}
                        alt={user.name || "User"}
                        className="w-8 h-8 rounded-full transition-transform duration-300 ease-in-out group-hover:scale-110"
                      />
                    ) : (
                      <span className="font-medium transition-transform duration-300 ease-in-out group-hover:scale-110">
                        {(user.name || "User").charAt(0).toUpperCase()}
                      </span>
                    )}
                  </div>
                  <div className="flex flex-col">
                    <span className="font-medium text-sm text-foreground transition-colors duration-300 ease-in-out">
                      {user.name || "User"}
                    </span>
                    {isAdmin && (
                      <span className="text-xs font-medium text-primary transition-colors duration-300 ease-in-out">
                        Admin
                      </span>
                    )}
                  </div>
                  <FiChevronDown className="ml-1 transition-transform duration-300 ease-in-out group-hover:rotate-180" />
                </div>

                <motion.div
                  initial={{ opacity: 0, y: 10, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  transition={{ duration: 0.25, ease: "easeOut" }}
                  className={`absolute right-0 mt-1 sm:mt-2 w-48 sm:w-52 md:w-56 rounded-lg shadow-xl py-1 sm:py-2 z-50 opacity-0 group-hover:opacity-100 invisible group-hover:visible transition-all duration-300 ease-in-out ${
                    theme === "dark" ? "bg-gray-800" : "bg-white"
                  }`}
                >
                  {/* User info section */}
                  <div className={`px-2 sm:px-3 md:px-4 py-1 sm:py-2 transition-colors duration-300 ease-in-out ${
                    theme === "dark" ? "border-b border-gray-700" : "border-b border-gray-200"
                  }`}>
                    <p className={`text-xs sm:text-sm font-medium transition-colors duration-300 ease-in-out ${
                      theme === "dark" ? "text-gray-200" : "text-gray-900"
                    }`}>
                      {user.name || "User"}
                    </p>
                    <p className={`text-xs transition-colors duration-300 ease-in-out ${
                      theme === "dark" ? "text-gray-400" : "text-gray-500"
                    }`}>{user.email}</p>
                    <div className="mt-1">
                      {isAdmin && (
                        <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium transition-all duration-300 ease-in-out ${
                          theme === "dark"
                            ? "bg-purple-900/50 text-purple-300"
                            : "bg-purple-100 text-purple-800"
                        }`}>
                          <FiShield className="mr-1" size={10} />
                          Admin
                        </span>
                      )}
                      {!isAdmin && (
                        <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium transition-all duration-300 ease-in-out ${
                          theme === "dark"
                            ? "bg-gray-700 text-gray-300"
                            : "bg-gray-100 text-gray-800"
                        }`}>
                          <FiUser className="mr-1" size={10} />
                          User
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Admin links */}
                  {isAdmin && (
                    <div className={`py-1 transition-colors duration-300 ease-in-out ${
                      theme === "dark" ? "border-b border-gray-700" : "border-b border-gray-200"
                    }`}>
                      <button
                        onClick={() => handleNavigation('/admin')}
                        className={`flex items-center px-4 py-2 text-sm transition-all duration-300 ease-in-out group ${
                          theme === "dark"
                            ? "text-gray-300 hover:bg-gray-700 hover:text-blue-400"
                            : "text-gray-700 hover:bg-blue-50 hover:text-blue-600"
                        }`}
                      >
                        <FiLayout className="mr-2 transition-transform duration-300 ease-in-out group-hover:scale-110" />
                        Admin Dashboard
                      </button>
                      <button
                        onClick={() => handleNavigation('/admin/users')}
                        className={`flex items-center px-4 py-2 text-sm transition-all duration-300 ease-in-out group ${
                          theme === "dark"
                            ? "text-gray-300 hover:bg-gray-700 hover:text-blue-400"
                            : "text-gray-700 hover:bg-blue-50 hover:text-blue-600"
                        }`}
                      >
                        <FiUser className="mr-2 transition-transform duration-300 ease-in-out group-hover:scale-110" />
                        Manage Users
                      </button>
                      <button
                        onClick={() => handleNavigation('/admin/blog/posts')}
                        className={`flex items-center px-4 py-2 text-sm transition-all duration-300 ease-in-out group ${
                          theme === "dark"
                            ? "text-gray-300 hover:bg-gray-700 hover:text-blue-400"
                            : "text-gray-700 hover:bg-blue-50 hover:text-blue-600"
                        }`}
                      >
                        <FiBook className="mr-2 transition-transform duration-300 ease-in-out group-hover:scale-110" />
                        Manage Blog
                      </button>
                    </div>
                  )}

                  {/* Removed editor links section as per new instructions */}

                  {/* Common user links */}
                  <div className="py-1">
                    {/* <Link
                      href="/profile"
                      className={`flex items-center px-4 py-2 text-sm transition-all duration-300 ease-in-out group ${
                        theme === "dark"
                          ? "text-gray-300 hover:bg-gray-700 hover:text-blue-400"
                          : "text-gray-700 hover:bg-blue-50 hover:text-blue-600"
                      }`}
                    >
                      <FiUser className="mr-2 transition-transform duration-300 ease-in-out group-hover:scale-110" />
                      My Profile
                    </Link> */}
                    {/* <Link
                      href="/settings"
                      className={`flex items-center px-4 py-2 text-sm transition-all duration-300 ease-in-out group ${
                        theme === "dark"
                          ? "text-gray-300 hover:bg-gray-700 hover:text-blue-400"
                          : "text-gray-700 hover:bg-blue-50 hover:text-blue-600"
                      }`}
                    >
                      <FiSettings className="mr-2 transition-transform duration-300 ease-in-out group-hover:scale-110 group-hover:rotate-45" />
                      Settings
                    </Link> */}
                    <button
                      onClick={handleLogout}
                      className={`flex items-center w-full text-left px-4 py-2 text-sm transition-all duration-300 ease-in-out group ${
                        theme === "dark"
                          ? "text-red-400 hover:bg-red-900/30"
                          : "text-red-600 hover:bg-red-50"
                      }`}
                      data-testid="logout-button"
                    >
                      <FiLogOut className="mr-2 transition-transform duration-300 ease-in-out group-hover:translate-x-1" />
                      Logout
                    </button>
                  </div>
                </motion.div>
              </li>
            )}

            {/* Smart Search Component */}
            <li>
              <SmartSearch className="ml-2" />
            </li>

         
          </ul>
        </nav>

        {/* Mobile menu overlay */}
        {mobileMenuOpen && (
          <div className="lg:hidden ">
            <div
              className="fixed inset-0  bg-black/50 z-40"
              aria-hidden="true"
            />
            <motion.div
              className="mobile-menu fixed inset-y-0 right-0 w-full max-w-[280px] sm:max-w-xs bg-gray-100 dark:bg-gray-900 border-l border-border shadow-xl z-50 overflow-y-auto"
              initial={{ x: "100%" }}
              animate={{ x: 0 }}
              exit={{ x: "100%" }}
              transition={{ type: "spring", damping: 25, stiffness: 300 }}
            >
              <div className="p-2 sm:p-3 md:p-4 space-y-4 sm:space-y-6">
                <div className="flex items-center justify-between mb-6">
                  <button
                    className="text-xl font-bold text-primary"
                    onClick={() => {
                      handleNavigation('/');
                      setMobileMenuOpen(false);
                    }}
                  >
                    PDF Tools
                  </button>
                  <div className="flex items-center gap-2">
                    {/* Dark Mode Toggle for Mobile */}
                    <Button
                      variant="ghost"
                      size="icon"
                      className={`transition-all duration-300 ease-in-out rounded-full ${
                        theme === "dark"
                          ? "text-gray-300 hover:bg-gray-700 hover:text-white"
                          : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                      }`}
                      onClick={toggleTheme}
                    >
                      {theme === "dark" ? (
                        <Sun className="h-5 w-5 transition-transform duration-500 ease-in-out hover:rotate-90" />
                      ) : (
                        <Moon className="h-5 w-5 transition-transform duration-500 ease-in-out hover:rotate-12" />
                      )}
                      <span className="sr-only">Toggle theme</span>
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => setMobileMenuOpen(false)}
                      aria-label="Close menu"
                    >
                      <X className="h-5 w-5" />
                    </Button>
                  </div>
                </div>

                {/* Mobile navigation items */}
                <nav className="space-y-6">
                  {/* Regular nav items */}
                  <div className="space-y-2">
                    {navItems.map((item) => (
                      <button
                        key={item.name}
                        className={`flex items-center py-2 px-3 rounded-md transition-all duration-200 ${
                          item.className || "text-foreground hover:text-primary hover:bg-primary/5"
                        }`}
                        onClick={() => {
                          handleNavigation(item.path);
                          setMobileMenuOpen(false);
                        }}
                      >
                        <span className="mr-3">{item.icon}</span>
                        <span>{item.name}</span>
                      </button>
                    ))}
                  </div>

                  {/* Tools section */}
                  <div className="pt-4 border-t border-border">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="text-sm font-semibold text-muted-foreground uppercase tracking-wider">
                        Tools
                      </h3>
                      <button
                        className="text-xs text-primary hover:underline"
                        onClick={() => {
                          handleNavigation('/tools');
                          setMobileMenuOpen(false);
                        }}
                      >
                        See more
                      </button>
                    </div>
                    <div className="space-y-1">
                      {/* Get 3 popular tools from all categories */}
                      {toolCategories.flatMap(category =>
                        category.tools
                      ).slice(0, 3).map((tool) => (
                        <button
                          key={tool.name}
                          className="flex items-center py-2 px-3 text-sm rounded-md text-foreground hover:text-primary hover:bg-primary/5 transition-all duration-200"
                          onClick={() => {
                            handleNavigation(tool.path);
                            setMobileMenuOpen(false);
                          }}
                        >
                          <span className="mr-3">{tool.icon}</span>
                          <span>{tool.name}</span>
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Calculators section */}
                  <div className="pt-4 border-t border-border">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="text-sm font-semibold text-muted-foreground uppercase tracking-wider">
                        Calculators
                      </h3>
                      <div className="flex space-x-2">
                        <button
                          className="text-xs text-primary hover:underline"
                          onClick={() => {
                            handleNavigation('/calculators');
                            setMobileMenuOpen(false);
                          }}
                        >
                          All Calculators
                        </button>
                        <button
                          className="text-xs text-primary hover:underline"
                          onClick={() => {
                            handleNavigation('/calculators');
                            setMobileMenuOpen(false);
                          }}
                        >
                          Categories
                        </button>
                      </div>
                    </div>
                    <div className="space-y-1">
                      {/* Get 3 popular calculators */}
                      {ALL_CALCULATORS.filter(calc => calc.popular).slice(0, 3).map((calculator) => (
                        <button
                          key={calculator.id}
                          className="flex items-center py-2 px-3 text-sm rounded-md text-foreground hover:text-primary hover:bg-primary/5 transition-all duration-200"
                          onClick={() => {
                            handleNavigation(`/calculators/${calculator.id}`);
                            setMobileMenuOpen(false);
                          }}
                        >
                          <span className="mr-3">{renderIcon(calculator.icon)}</span>
                          <span>{calculator.title}</span>
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Blog section */}
                  <div className="pt-4 border-t border-border">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="text-sm font-semibold text-muted-foreground uppercase tracking-wider">
                        Blog
                      </h3>
                      <div className="flex space-x-2">
                        <button
                          className="text-xs text-purple-600 dark:text-purple-400 hover:underline"
                          onClick={() => {
                            handleNavigation('/blog');
                            setMobileMenuOpen(false);
                          }}
                        >
                          Home
                        </button>
                        <button
                          className="text-xs text-purple-600 dark:text-purple-400 hover:underline"
                          onClick={() => {
                            handleNavigation('/blog/all');
                            setMobileMenuOpen(false);
                          }}
                        >
                          All Articles
                        </button>
                      </div>
                    </div>
                    <div className="space-y-1">
                      <button
                        className="flex items-center py-2 px-3 text-sm rounded-md text-foreground hover:text-purple-600 dark:hover:text-purple-400 hover:bg-purple-50 dark:hover:bg-purple-900/20 transition-all duration-200"
                        onClick={() => {
                          handleNavigation('/blog');
                          setMobileMenuOpen(false);
                        }}
                      >
                        <FiHome className="mr-3" />
                        <span>Blog Home</span>
                      </button>
                      <button
                        className="flex items-center py-2 px-3 text-sm rounded-md text-foreground hover:text-purple-600 dark:hover:text-purple-400 hover:bg-purple-50 dark:hover:bg-purple-900/20 transition-all duration-200"
                        onClick={() => {
                          handleNavigation('/blog/all');
                          setMobileMenuOpen(false);
                        }}
                      >
                        <FiGrid className="mr-3" />
                        <span>All Articles</span>
                      </button>
                      {/* <button
                        className="flex items-center py-2 px-3 text-sm rounded-md text-foreground hover:text-purple-600 dark:hover:text-purple-400 hover:bg-purple-50 dark:hover:bg-purple-900/20 transition-all duration-200"
                        onClick={() => {
                          handleNavigation('/blog/categories');
                          setMobileMenuOpen(false);
                        }}
                      >
                        <FiTag className="mr-3" />
                        <span>Categories</span>
                      </button> */}


                    </div>
                  </div>

                  {/* User section for mobile */}
                  {user && (
                    <div className="pt-4 border-t border-border">
                      <div className="flex items-center px-3 py-2 mb-3">
                        <div className="w-10 h-10 rounded-full bg-primary/20 text-primary flex items-center justify-center mr-3">
                          {user.image ? (
                            <img
                              src={user.image}
                              alt={user.name || "User"}
                              className="w-10 h-10 rounded-full"
                            />
                          ) : (
                            <span className="font-medium text-lg">
                              {(user.name || "User").charAt(0).toUpperCase()}
                            </span>
                          )}
                        </div>
                        <div>
                          <p className="font-medium text-foreground">
                            {user.name || "User"}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {user.email}
                          </p>
                        </div>
                      </div>

                      {isAdmin && (
                        <div className="space-y-1 mb-3">
                          <button
                            className="flex items-center py-2 px-3 text-sm rounded-md text-foreground hover:text-primary hover:bg-primary/5 transition-all duration-200"
                            onClick={() => {
                              handleNavigation('/admin');
                              setMobileMenuOpen(false);
                            }}
                          >
                            <FiLayout className="mr-3" />
                            Admin Dashboard
                          </button>
                          <button
                            className="flex items-center py-2 px-3 text-sm rounded-md text-foreground hover:text-primary hover:bg-primary/5 transition-all duration-200"
                            onClick={() => {
                              handleNavigation('/admin/users');
                              setMobileMenuOpen(false);
                            }}
                          >
                            <FiUser className="mr-3" />
                            Manage Users
                          </button>
                          <button
                            className="flex items-center py-2 px-3 text-sm rounded-md text-foreground hover:text-primary hover:bg-primary/5 transition-all duration-200"
                            onClick={() => {
                              handleNavigation('/admin/blog');
                              setMobileMenuOpen(false);
                            }}
                          >
                            <FiBook className="mr-3" />
                            Manage Blog
                          </button>
                        </div>
                      )}

                      <div className="space-y-1">
                        {/* <button
                          className="flex items-center py-2 px-3 text-sm rounded-md text-foreground hover:text-primary hover:bg-primary/5 transition-all duration-200"
                          onClick={() => {
                            handleNavigation('/profile');
                            setMobileMenuOpen(false);
                          }}
                        >
                          <FiUser className="mr-3" />
                          My Profile
                        </button> */}
                        {/* <button
                          className="flex items-center py-2 px-3 text-sm rounded-md text-foreground hover:text-primary hover:bg-primary/5 transition-all duration-200"
                          onClick={() => {
                            handleNavigation('/settings');
                            setMobileMenuOpen(false);
                          }}
                        >
                          <FiSettings className="mr-3" />
                          Settings
                        </button> */}
                        <button
                          onClick={(e) => {
                            handleLogout(e);
                            setMobileMenuOpen(false);
                          }}
                          className="flex items-center w-full text-left py-2 px-3 text-sm rounded-md text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 transition-all duration-200"
                        >
                          <FiLogOut className="mr-3" />
                          Logout
                        </button>
                      </div>
                    </div>
                  )}


                </nav>
              </div>
            </motion.div>
          </div>
        )}
      </div>
    </motion.header>
  );
};

export default Header;
