#!/bin/bash

# =============================================================================
# TOOLRAPTER - VPS INFRASTRUCTURE SETUP SCRIPT
# =============================================================================
# Enterprise-grade VPS setup for Hostinger Ubuntu 22.04 LTS
# Domain: toolrapter.com | VPS: ************
# Usage: ./scripts/setup-vps.sh

set -e  # Exit on any error

# =============================================================================
# CONFIGURATION
# =============================================================================
DOMAIN="toolrapter.com"
APP_NAME="toolrapter"
APP_DIR="/var/www/toolrapter"
LOG_DIR="/var/log/pm2"
BACKUP_DIR="/var/backups/toolrapter"
NGINX_SITES_AVAILABLE="/etc/nginx/sites-available"
NGINX_SITES_ENABLED="/etc/nginx/sites-enabled"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root"
        exit 1
    fi
}

# =============================================================================
# SYSTEM SETUP
# =============================================================================
update_system() {
    log_info "Updating system packages..."
    apt update && apt upgrade -y
    apt install -y curl wget git unzip software-properties-common
    log_success "System updated successfully"
}

install_nodejs() {
    log_info "Installing Node.js 20..."
    curl -fsSL https://deb.nodesource.com/setup_20.x | bash -
    apt install -y nodejs
    
    # Install pnpm for faster package management
    npm install -g pnpm pm2
    
    # Verify installation
    node_version=$(node --version)
    npm_version=$(npm --version)
    log_success "Node.js $node_version and npm $npm_version installed"
}

install_nginx() {
    log_info "Installing and configuring Nginx..."
    apt install -y nginx
    
    # Enable and start Nginx
    systemctl enable nginx
    systemctl start nginx
    
    # Configure firewall
    ufw allow 'Nginx Full'
    ufw allow OpenSSH
    ufw --force enable
    
    log_success "Nginx installed and configured"
}

install_certbot() {
    log_info "Installing Certbot for SSL certificates..."
    apt install -y certbot python3-certbot-nginx
    log_success "Certbot installed"
}

# =============================================================================
# APPLICATION SETUP
# =============================================================================
setup_directories() {
    log_info "Creating application directories..."
    
    # Create application directory
    mkdir -p "$APP_DIR"
    mkdir -p "$LOG_DIR"
    mkdir -p "$BACKUP_DIR"
    
    # Set proper permissions
    chown -R www-data:www-data "$APP_DIR"
    chown -R www-data:www-data "$LOG_DIR"
    chown -R root:root "$BACKUP_DIR"
    
    chmod -R 755 "$APP_DIR"
    chmod -R 755 "$LOG_DIR"
    chmod -R 700 "$BACKUP_DIR"
    
    log_success "Directories created and configured"
}

configure_nginx() {
    log_info "Configuring Nginx for $DOMAIN..."
    
    # Copy nginx configuration
    if [ -f "nginx.conf" ]; then
        cp nginx.conf "$NGINX_SITES_AVAILABLE/$DOMAIN"
    else
        log_error "nginx.conf not found in current directory"
        exit 1
    fi
    
    # Enable site
    ln -sf "$NGINX_SITES_AVAILABLE/$DOMAIN" "$NGINX_SITES_ENABLED/"
    
    # Remove default site
    rm -f "$NGINX_SITES_ENABLED/default"
    
    # Test configuration
    nginx -t
    
    # Reload Nginx
    systemctl reload nginx
    
    log_success "Nginx configured for $DOMAIN"
}

setup_ssl() {
    log_info "Setting up SSL certificate for $DOMAIN..."
    
    # Obtain SSL certificate
    certbot --nginx -d "$DOMAIN" -d "www.$DOMAIN" --non-interactive --agree-tos --email admin@"$DOMAIN"
    
    # Setup auto-renewal
    (crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet") | crontab -
    
    log_success "SSL certificate configured and auto-renewal enabled"
}

# =============================================================================
# SECURITY SETUP
# =============================================================================
configure_firewall() {
    log_info "Configuring firewall..."
    
    # Reset UFW to defaults
    ufw --force reset
    
    # Set default policies
    ufw default deny incoming
    ufw default allow outgoing
    
    # Allow essential services
    ufw allow ssh
    ufw allow 'Nginx Full'
    ufw allow 80/tcp
    ufw allow 443/tcp
    
    # Enable firewall
    ufw --force enable
    
    log_success "Firewall configured"
}

setup_fail2ban() {
    log_info "Installing and configuring Fail2Ban..."
    
    apt install -y fail2ban
    
    # Create custom jail configuration
    cat > /etc/fail2ban/jail.local << 'EOF'
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3

[sshd]
enabled = true
port = ssh
filter = sshd
logpath = /var/log/auth.log
maxretry = 3

[nginx-http-auth]
enabled = true
filter = nginx-http-auth
port = http,https
logpath = /var/log/nginx/error.log

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
port = http,https
logpath = /var/log/nginx/error.log
maxretry = 10
EOF
    
    # Start and enable Fail2Ban
    systemctl enable fail2ban
    systemctl start fail2ban
    
    log_success "Fail2Ban configured"
}

# =============================================================================
# PM2 SETUP
# =============================================================================
setup_pm2() {
    log_info "Configuring PM2..."
    
    # Setup PM2 startup script
    pm2 startup systemd -u root --hp /root
    
    # Save PM2 configuration
    pm2 save
    
    log_success "PM2 configured for auto-startup"
}

# =============================================================================
# MONITORING SETUP
# =============================================================================
setup_monitoring() {
    log_info "Setting up monitoring and logging..."
    
    # Create log rotation configuration
    cat > /etc/logrotate.d/toolrapter << 'EOF'
/var/log/pm2/*.log {
    daily
    missingok
    rotate 52
    compress
    notifempty
    create 644 www-data www-data
    postrotate
        pm2 reloadLogs
    endscript
}

/var/log/nginx/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data adm
    sharedscripts
    postrotate
        systemctl reload nginx
    endscript
}
EOF
    
    log_success "Monitoring and logging configured"
}

# =============================================================================
# MAIN EXECUTION
# =============================================================================
main() {
    log_info "Starting VPS setup for $DOMAIN..."
    
    # Check if running as root
    check_root
    
    # System setup
    update_system
    install_nodejs
    install_nginx
    install_certbot
    
    # Application setup
    setup_directories
    configure_nginx
    
    # Security setup
    configure_firewall
    setup_fail2ban
    
    # PM2 setup
    setup_pm2
    
    # Monitoring setup
    setup_monitoring
    
    # SSL setup (run last as it requires domain to be pointing to server)
    log_warning "SSL setup skipped - run 'certbot --nginx -d $DOMAIN -d www.$DOMAIN' after DNS is configured"
    
    log_success "VPS setup completed successfully!"
    log_info "Next steps:"
    log_info "1. Point your domain DNS to this server IP"
    log_info "2. Run SSL setup: certbot --nginx -d $DOMAIN -d www.$DOMAIN"
    log_info "3. Deploy your application using GitHub Actions"
}

# Run main function
main "$@"
