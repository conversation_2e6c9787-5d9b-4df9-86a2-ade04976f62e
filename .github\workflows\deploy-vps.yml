# =============================================================================
# TOOLRAPTER - ENTERPRISE VPS DEPLOYMENT PIPELINE
# =============================================================================
# Production-ready GitHub Actions workflow for Hostinger VPS deployment
# Features: Security scanning, testing, zero-downtime deployment, rollback
# Target: toolrapter.com on Hostinger VPS (************)

name: 🚀 Enterprise VPS Deployment

on:
  push:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'production'
        type: choice
        options:
          - production
          - staging
      skip_tests:
        description: 'Skip tests (emergency deployment)'
        required: false
        default: false
        type: boolean
      rollback:
        description: 'Rollback to previous version'
        required: false
        default: false
        type: boolean

# Concurrency control - prevent multiple deployments
concurrency:
  group: vps-deployment-${{ github.ref }}
  cancel-in-progress: false

env:
  NODE_VERSION: '20'
  DEPLOYMENT_TIMEOUT: '900'
  HEALTH_CHECK_RETRIES: '15'
  HEALTH_CHECK_DELAY: '20'
  PM2_APP_NAME: 'toolrapter'
  DOMAIN: 'toolrapter.com'
  VPS_USER: 'root'
  VPS_HOST: '************'

jobs:
  # =============================================================================
  # SECURITY & VULNERABILITY SCANNING
  # =============================================================================
  security-scan:
    name: 🔒 Security Scan
    runs-on: ubuntu-latest
    if: ${{ !inputs.skip_tests && !inputs.rollback }}
    timeout-minutes: 10

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🔍 Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: 📊 Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

  # =============================================================================
  # BUILD & TEST PIPELINE
  # =============================================================================
  build-and-test:
    name: 🏗️ Build & Test
    runs-on: ubuntu-latest
    if: ${{ !inputs.skip_tests && !inputs.rollback }}
    timeout-minutes: 20

    outputs:
      build-success: ${{ steps.build.outcome == 'success' }}

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'
          cache-dependency-path: 'pnpm-lock.yaml'

      - name: 📥 Install dependencies
        run: |
          echo "Using pnpm for faster installation..."
          pnpm install --frozen-lockfile --prefer-offline
          pnpm run download-fonts

      - name: 🔧 Type check
        run: pnpm exec tsc --noEmit

      - name: 🧪 Run tests
        run: |
          pnpm run test:coverage
          pnpm run lint

      - name: 🏗️ Build application
        id: build
        run: |
          # Set build start time for performance tracking
          BUILD_START=$(date +%s)
          echo "BUILD_START=$BUILD_START" >> $GITHUB_ENV

          # Build with optimizations
          pnpm run build

          # Calculate build time
          BUILD_END=$(date +%s)
          BUILD_TIME=$((BUILD_END - BUILD_START))
          echo "Build completed in ${BUILD_TIME} seconds"

          # Fail if build takes longer than 20 seconds (requirement)
          if [ $BUILD_TIME -gt 20 ]; then
            echo "❌ Build time exceeded 20 seconds requirement!"
            exit 1
          fi

          # Check bundle size
          if [ -d ".next" ]; then
            BUNDLE_SIZE=$(du -sh .next | cut -f1)
            echo "Bundle size: $BUNDLE_SIZE"
          fi
        env:
          NODE_ENV: production
          NEXT_TELEMETRY_DISABLED: 1
          # Performance optimizations
          NEXT_PRIVATE_STANDALONE: true

      - name: 📦 Create deployment package
        if: steps.build.outcome == 'success'
        run: |
          tar -czf deployment.tar.gz \
            .next/ \
            public/ \
            package.json \
            pnpm-lock.yaml \
            next.config.js \
            scripts/ \
            ecosystem.config.js \
            --exclude=node_modules

      - name: 📤 Upload build artifacts
        if: steps.build.outcome == 'success'
        uses: actions/upload-artifact@v4
        with:
          name: deployment-package
          path: deployment.tar.gz
          retention-days: 7

  # =============================================================================
  # DEPLOYMENT TO VPS
  # =============================================================================
  deploy:
    name: 🚀 Deploy to VPS
    runs-on: ubuntu-latest
    needs: [security-scan, build-and-test]
    if: always() && (needs.build-and-test.result == 'success' || inputs.skip_tests || inputs.rollback)
    environment: ${{ inputs.environment || 'production' }}
    timeout-minutes: 30
    env:
      VPS_SSH_KEY: ${{ secrets.VPS_SSH_KEY }}
      VPS_USER: ${{ secrets.VPS_USER }}
      VPS_HOST: ${{ secrets.VPS_HOST }}
      MONGODB_URI: ${{ secrets.MONGODB_URI }}
      NEXTAUTH_SECRET: ${{ secrets.NEXTAUTH_SECRET }}
      UPSTASH_REDIS_REST_URL: ${{ secrets.UPSTASH_REDIS_REST_URL }}
      UPSTASH_REDIS_REST_TOKEN: ${{ secrets.UPSTASH_REDIS_REST_TOKEN }}
      GOOGLE_CLIENT_ID: ${{ secrets.GOOGLE_CLIENT_ID }}
      GOOGLE_CLIENT_SECRET: ${{ secrets.GOOGLE_CLIENT_SECRET }}

    steps:
      - name: 🔐 Validate Required Secrets
        run: |
          echo "🔍 Validating required repository secrets..."

          # Check if all required secrets are configured
          MISSING_SECRETS=()

          if [ -z "${{ secrets.VPS_SSH_KEY }}" ]; then
            MISSING_SECRETS+=("VPS_SSH_KEY")
          fi

          if [ -z "${{ secrets.VPS_USER }}" ]; then
            MISSING_SECRETS+=("VPS_USER")
          fi

          if [ -z "${{ secrets.VPS_HOST }}" ]; then
            MISSING_SECRETS+=("VPS_HOST")
          fi

          if [ -z "${{ secrets.MONGODB_URI }}" ]; then
            MISSING_SECRETS+=("MONGODB_URI")
          fi

          if [ -z "${{ secrets.NEXTAUTH_SECRET }}" ]; then
            MISSING_SECRETS+=("NEXTAUTH_SECRET")
          fi

          if [ -z "${{ secrets.UPSTASH_REDIS_REST_URL }}" ]; then
            MISSING_SECRETS+=("UPSTASH_REDIS_REST_URL")
          fi

          if [ -z "${{ secrets.UPSTASH_REDIS_REST_TOKEN }}" ]; then
            MISSING_SECRETS+=("UPSTASH_REDIS_REST_TOKEN")
          fi

          if [ -z "${{ secrets.GOOGLE_CLIENT_ID }}" ]; then
            MISSING_SECRETS+=("GOOGLE_CLIENT_ID")
          fi

          if [ -z "${{ secrets.GOOGLE_CLIENT_SECRET }}" ]; then
            MISSING_SECRETS+=("GOOGLE_CLIENT_SECRET")
          fi

          # Report results
          if [ ${#MISSING_SECRETS[@]} -eq 0 ]; then
            echo "✅ All required secrets are configured!"
          else
            echo "❌ Missing required repository secrets:"
            for secret in "${MISSING_SECRETS[@]}"; do
              echo "  - $secret"
            done
            echo ""
            echo "📖 Please configure these secrets in your repository settings:"
            echo "   Settings → Secrets and variables → Actions → Repository secrets"
            echo ""
            echo "📚 For detailed setup instructions, see:"
            echo "   docs/GITHUB_SECRETS_SETUP.md"
            exit 1
          fi

      - name: 📥 Checkout code
        uses: actions/checkout@v4
        if: ${{ !inputs.rollback }}

      - name: 📦 Download deployment package
        if: ${{ !inputs.rollback }}
        uses: actions/download-artifact@v4
        with:
          name: deployment-package

      - name: 🔐 Setup SSH key
        uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: ${{ secrets.VPS_SSH_KEY }}

      - name: 📤 Upload to VPS
        if: ${{ !inputs.rollback }}
        run: |
          scp -o StrictHostKeyChecking=no \
            deployment.tar.gz \
            root@************:/tmp/

      - name: 🔄 Create backup of current deployment
        run: |
          ssh -o StrictHostKeyChecking=no \
            root@************ << 'EOF'

          APP_DIR="/var/www/toolrapter"
          BACKUP_DIR="/var/backups/toolrapter"
          TIMESTAMP=$(date +%Y%m%d_%H%M%S)

          # Create backup directory if it doesn't exist
          sudo mkdir -p "$BACKUP_DIR"

          # Create backup of current deployment
          if [ -d "$APP_DIR" ]; then
            echo "📦 Creating backup of current deployment..."
            sudo tar -czf "$BACKUP_DIR/backup_$TIMESTAMP.tar.gz" -C "$APP_DIR" .
            echo "✅ Backup created: backup_$TIMESTAMP.tar.gz"

            # Keep only last 5 backups
            sudo find "$BACKUP_DIR" -name "backup_*.tar.gz" -type f | sort -r | tail -n +6 | sudo xargs rm -f
          fi
          EOF

      - name: 🚀 Deploy on VPS
        if: ${{ !inputs.rollback }}
        run: |
          ssh -o StrictHostKeyChecking=no \
            root@************ << 'EOF'

          # Set variables
          APP_DIR="/var/www/toolrapter"
          BACKUP_DIR="/var/backups/toolrapter"
          
          # Create backup of current deployment
          if [ -d "$APP_DIR" ]; then
            echo "📦 Creating backup..."
            sudo mkdir -p "$BACKUP_DIR"
            sudo cp -r "$APP_DIR" "$BACKUP_DIR/backup-$(date +%Y%m%d-%H%M%S)"
          fi
          
          # Prepare deployment directory
          sudo mkdir -p "$APP_DIR"
          cd "$APP_DIR"
          
          # Extract new deployment
          echo "📤 Extracting deployment package..."
          sudo tar -xzf /tmp/deployment.tar.gz -C "$APP_DIR"
          sudo chown -R root:root "$APP_DIR"

          # Install production dependencies with performance optimization
          echo "📥 Installing dependencies..."
          # Install pnpm if not available
          if ! command -v pnpm &> /dev/null; then
            npm install -g pnpm
          fi
          pnpm install --frozen-lockfile --prod --prefer-offline

          # Download fonts
          pnpm run download-fonts

          # Create environment file with secrets
          echo "🔐 Setting up environment variables..."
          cat > .env.production << 'ENV_EOF'
          NODE_ENV=production
          NEXT_TELEMETRY_DISABLED=1
          MONGODB_URI=${{ secrets.MONGODB_URI }}
          NEXTAUTH_SECRET=${{ secrets.NEXTAUTH_SECRET }}
          UPSTASH_REDIS_REST_URL=${{ secrets.UPSTASH_REDIS_REST_URL }}
          UPSTASH_REDIS_REST_TOKEN=${{ secrets.UPSTASH_REDIS_REST_TOKEN }}
          NEXT_PUBLIC_PRODUCTION_URL=https://toolrapter.com
          NEXT_PUBLIC_APP_ENV=production
          ENV_EOF

          # Restart application with PM2
          echo "🔄 Restarting application..."
          pm2 stop toolrapter || true
          pm2 delete toolrapter || true
          pm2 start npm --name "toolrapter" -- start
          pm2 save
          
          # Reload Nginx
          echo "🔄 Reloading Nginx..."
          sudo nginx -t && sudo systemctl reload nginx
          
          # Health check
          echo "🏥 Performing health check..."
          sleep 10
          if curl -f http://localhost:3000/api/health; then
            echo "✅ Deployment successful!"
          else
            echo "❌ Health check failed!"
            exit 1
          fi

          # Cleanup
          rm -f /tmp/deployment.tar.gz

          EOF

      - name: 🧹 Cleanup local files
        if: always()
        run: rm -f deployment.tar.gz

  # =============================================================================
  # POST-DEPLOYMENT VERIFICATION
  # =============================================================================
  verify:
    name: ✅ Verify Deployment
    runs-on: ubuntu-latest
    needs: deploy
    if: success()
    timeout-minutes: 10

    steps:
      - name: 🏥 Health check
        run: |
          echo "🏥 Performing external health check..."

          # Wait for application to be ready
          sleep ${{ env.HEALTH_CHECK_DELAY }}

          # Check if application is responding
          if curl -f --max-time 30 https://toolrapter.com/api/health; then
            echo "✅ Application is healthy!"
          else
            echo "❌ Application health check failed!"
            exit 1
          fi

      - name: 🔍 Performance check
        run: |
          echo "📊 Running basic performance check..."

          # Check response time
          RESPONSE_TIME=$(curl -o /dev/null -s -w '%{time_total}' https://toolrapter.com)
          echo "⏱️ Response time: ${RESPONSE_TIME}s"

          # Fail if response time is too slow (> 5 seconds)
          if (( $(echo "$RESPONSE_TIME > 5.0" | bc -l) )); then
            echo "❌ Response time too slow!"
            exit 1
          fi

      - name: 📢 Deployment notification
        if: success()
        run: |
          echo "🎉 VPS deployment completed successfully!"
          echo "🌐 Application is live at: https://toolrapter.com"
          echo "📊 Environment: ${{ inputs.environment || 'production' }}"

  # =============================================================================
  # ROLLBACK (MANUAL TRIGGER)
  # =============================================================================
  rollback:
    name: ⏪ Rollback Deployment
    runs-on: ubuntu-latest
    if: failure() && needs.deploy.result == 'failure'
    needs: [deploy, verify]
    env:
      VPS_SSH_KEY: ${{ secrets.VPS_SSH_KEY }}
      VPS_USER: ${{ secrets.VPS_USER }}
      VPS_HOST: ${{ secrets.VPS_HOST }}

    steps:
      - name: 🔐 Setup SSH key
        uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: ${{ secrets.VPS_SSH_KEY }}

      - name: ⏪ Rollback to previous version
        run: |
          ssh -o StrictHostKeyChecking=no \
            root@************ << 'EOF'

          APP_DIR="/var/www/toolrapter"
          BACKUP_DIR="/var/backups/toolrapter"
          
          # Find latest backup
          LATEST_BACKUP=$(ls -t "$BACKUP_DIR" | head -n1)
          
          if [ -n "$LATEST_BACKUP" ]; then
            echo "⏪ Rolling back to: $LATEST_BACKUP"
            
            # Stop current application
            pm2 stop toolrapter || true

            # Restore from backup
            sudo rm -rf "$APP_DIR"
            sudo cp -r "$BACKUP_DIR/$LATEST_BACKUP" "$APP_DIR"
            sudo chown -R root:root "$APP_DIR"

            # Restart application
            cd "$APP_DIR"
            pm2 start npm --name "toolrapter" -- start
            pm2 save
            
            echo "✅ Rollback completed!"
          else
            echo "❌ No backup found for rollback!"
            exit 1
          fi
          
          EOF

      - name: 📢 Rollback notification
        if: success()
        run: |
          echo "⏪ Rollback completed successfully!"
          echo "🔄 Application restored to previous version"
