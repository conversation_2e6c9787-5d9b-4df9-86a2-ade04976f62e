import { ALL_TOOLS } from '@/data/tools';
import connectToDatabase from '@/lib/db';
import BlogPost from '@/models/BlogPost';

// Tool interface for related tools
export interface RelatedTool {
  id: string;
  title: string;
  description: string;
  icon: string;
  category: string;
  path: string;
}

// Blog post interface for related posts
export interface RelatedBlogPost {
  _id: string;
  title: string;
  slug: string;
  excerpt: string;
  featuredImage: string;
  categories: string[];
  tags: string[];
  publishedAt: string;
  author: {
    name: string;
  };
}

// Get related tools based on blog post category and content
export function getRelatedTools(category: string, content: string, tags: string[] = []): RelatedTool[] {
  const relatedTools: RelatedTool[] = [];
  const contentLower = content.toLowerCase();
  const categoryLower = category.toLowerCase();

  // Category-based tool mapping
  const categoryToolMap: Record<string, string[]> = {
    'pdf': ['pdf-to-word', 'compress-pdf', 'merge-pdf', 'split-pdf'],
    'office': ['word-to-pdf', 'excel-to-pdf', 'powerpoint-to-pdf'],
    'conversion': ['pdf-to-word', 'pdf-to-excel', 'pdf-to-powerpoint'],
    'productivity': ['compress-pdf', 'merge-pdf', 'split-pdf'],
    'security': ['pdf-password-protect', 'pdf-unlock'],
    'image': ['pdf-to-jpg', 'jpg-to-pdf'],
    'web': ['html-to-pdf', 'url-to-pdf'],
    'general': ['pdf-to-word', 'compress-pdf', 'merge-pdf']
  };

  // Content-based tool suggestions
  const contentKeywords: Record<string, string[]> = {
    'pdf': ['pdf-to-word', 'compress-pdf', 'merge-pdf'],
    'word': ['word-to-pdf', 'pdf-to-word'],
    'excel': ['excel-to-pdf', 'pdf-to-excel'],
    'powerpoint': ['powerpoint-to-pdf', 'pdf-to-powerpoint'],
    'compress': ['compress-pdf'],
    'merge': ['merge-pdf'],
    'split': ['split-pdf'],
    'convert': ['pdf-to-word', 'word-to-pdf'],
    'image': ['pdf-to-jpg', 'jpg-to-pdf'],
    'security': ['pdf-password-protect'],
    'password': ['pdf-password-protect', 'pdf-unlock']
  };

  // Get tools by category
  const categoryTools = categoryToolMap[categoryLower] || categoryToolMap['general'];
  
  // Get tools by content keywords
  const contentTools: string[] = [];
  Object.entries(contentKeywords).forEach(([keyword, tools]) => {
    if (contentLower.includes(keyword)) {
      contentTools.push(...tools);
    }
  });

  // Get tools by tags
  const tagTools: string[] = [];
  tags.forEach(tag => {
    const tagLower = tag.toLowerCase();
    if (contentKeywords[tagLower]) {
      tagTools.push(...contentKeywords[tagLower]);
    }
  });

  // Combine and deduplicate tool IDs
  const allToolIds = Array.from(new Set([...categoryTools, ...contentTools, ...tagTools]));

  // Map to actual tool data
  allToolIds.forEach(toolId => {
    const tool = ALL_TOOLS.find(t => t.id === toolId);
    if (tool && relatedTools.length < 4) {
      relatedTools.push({
        id: tool.id,
        title: tool.title,
        description: tool.description,
        icon: tool.icon || '🔧',
        category: tool.category,
        path: `/tools/${tool.id}`
      });
    }
  });

  // If we don't have enough tools, add some popular ones
  if (relatedTools.length < 3) {
    const popularTools = ['pdf-to-word', 'compress-pdf', 'merge-pdf', 'word-to-pdf'];
    popularTools.forEach(toolId => {
      const tool = ALL_TOOLS.find(t => t.id === toolId);
      if (tool && !relatedTools.find(rt => rt.id === toolId) && relatedTools.length < 4) {
        relatedTools.push({
          id: tool.id,
          title: tool.title,
          description: tool.description,
          icon: tool.icon || '🔧',
          category: tool.category,
          path: `/tools/${tool.id}`
        });
      }
    });
  }

  return relatedTools.slice(0, 4);
}

// Get related blog posts based on category and tags
export async function getRelatedBlogPosts(
  currentSlug: string, 
  category: string, 
  tags: string[] = []
): Promise<RelatedBlogPost[]> {
  try {
    await connectToDatabase();

    // Build query to find related posts
    const query: any = {
      slug: { $ne: currentSlug }, // Exclude current post
      status: 'published',
      $or: [
        { categories: { $in: [category] } },
        { tags: { $in: tags } }
      ]
    };

    const relatedPosts = await BlogPost.find(query)
      .sort({ publishedAt: -1, createdAt: -1 })
      .limit(6)
      .populate('authorId', 'name')
      .select('title slug description featuredImage categories tags publishedAt authorId')
      .lean();

    return relatedPosts.map(post => ({
      _id: post._id.toString(),
      title: post.title,
      slug: post.slug,
      excerpt: post.description || post.title.substring(0, 100) + '...',
      featuredImage: post.featuredImage || '/blog-placeholder.jpg',
      categories: post.categories || [],
      tags: post.tags || [],
      publishedAt: post.publishedAt ? new Date(post.publishedAt).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      }) : new Date().toLocaleDateString(),
      author: {
        name: post.authorId?.name || 'Admin'
      }
    }));

  } catch (error) {
    console.error('Error fetching related blog posts:', error);
    return [];
  }
}

// Get category icon and color
export function getCategoryStyle(category: string): { icon: string; color: string; bgColor: string } {
  const categoryStyles: Record<string, { icon: string; color: string; bgColor: string }> = {
    'pdf': { icon: '📄', color: 'text-red-600', bgColor: 'bg-red-100 dark:bg-red-900/20' },
    'office': { icon: '💼', color: 'text-blue-600', bgColor: 'bg-blue-100 dark:bg-blue-900/20' },
    'productivity': { icon: '⚡', color: 'text-yellow-600', bgColor: 'bg-yellow-100 dark:bg-yellow-900/20' },
    'security': { icon: '🔒', color: 'text-green-600', bgColor: 'bg-green-100 dark:bg-green-900/20' },
    'conversion': { icon: '🔄', color: 'text-purple-600', bgColor: 'bg-purple-100 dark:bg-purple-900/20' },
    'image': { icon: '🖼️', color: 'text-pink-600', bgColor: 'bg-pink-100 dark:bg-pink-900/20' },
    'web': { icon: '🌐', color: 'text-cyan-600', bgColor: 'bg-cyan-100 dark:bg-cyan-900/20' },
    'general': { icon: '📝', color: 'text-gray-600', bgColor: 'bg-gray-100 dark:bg-gray-900/20' }
  };

  return categoryStyles[category.toLowerCase()] || categoryStyles['general'];
}
